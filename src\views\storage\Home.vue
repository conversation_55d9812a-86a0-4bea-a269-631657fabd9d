<script setup>
import { ref, computed, onMounted, reactive } from 'vue'
import { OfficeBuilding, Refresh, Edit, Delete, Close, Check } from '@element-plus/icons-vue'
import axios from 'axios'
import { ElMessageBox, ElMessage } from 'element-plus'
import http from '../../api/http'

// --- 仓库和库区数据 ---
// 仓库列表
const warehouseList = ref([])
const currentWarehouse = ref('')

// 库区列表
const zoneList = ref([])
const currentZone = ref('')

// 获取仓库列表
const fetchWarehouseList = async () => {
  try {
    const response = await http.post('/api/Wms_Warehouse/GetAllData', {}, true)
    if (response.status === 0 && Array.isArray(response.rows)) {
      warehouseList.value = response.rows
      // 默认选中第一项
      if (warehouseList.value.length > 0) {
        currentWarehouse.value = warehouseList.value[0].WarehouseId
        // 加载该仓库下的库区
        await fetchZoneList(currentWarehouse.value)
      }
    } else {
      console.error('获取仓库列表失败:', response)
    }
  } catch (error) {
    console.error('获取仓库列表出错:', error)
  }
}

// 获取库区列表
const fetchZoneList = async (warehouseId) => {
  try {
    const response = await http.post('/api/Wms_Zone/GetAllData', { WarehouseId: warehouseId }, true)
    if (response.status === 0 && Array.isArray(response.rows)) {
      zoneList.value = response.rows
      // 默认选中第一项
      if (zoneList.value.length > 0) {
        currentZone.value = zoneList.value[0].ZoneId
      } else {
        currentZone.value = ''
      }
    } else {
      console.error('获取库区列表失败:', response)
      zoneList.value = []
      currentZone.value = ''
    }
  } catch (error) {
    console.error('获取库区列表出错:', error)
    zoneList.value = []
    currentZone.value = ''
  }
}

// 监听仓库变化，加载对应库区
const handleWarehouseChange = () => {
  if (currentWarehouse.value) {
    fetchZoneList(currentWarehouse.value)
  } else {
    zoneList.value = []
    currentZone.value = ''
  }
}

// --- 仓库配置 ---
// 使用ref定义动态仓库规格
const warehouseRows = ref(6) // 默认值
const warehouseCols = ref(20) // 默认值
const warehouseLayers = ref(8) // 默认值
const warehouseData = ref([])

// 从API获取库位数据
const fetchWarehouseData = async () => {
  try {

    const apiData = await http.post('/api/Wms_Position/GetAllData', {}, true)

    if (apiData.status === 0 && Array.isArray(apiData.rows)) {
      // 计算仓库规格
      calculateWarehouseDimensions(apiData.rows)
      processWarehouseData(apiData.rows)
    } else {
      console.error('获取库位数据失败:', apiData)
      ElMessage.error('获取库位数据失败')
      // generateWarehouseData()
    }
  } catch (error) {
    console.error('获取库位数据出错:', error)
    ElMessage.error('获取库位数据出错')
    // generateWarehouseData()
  }
}

// 计算仓库尺寸
const calculateWarehouseDimensions = (positionRows) => {
  // 如果没有数据，使用默认值
  if (!positionRows || positionRows.length === 0) return

  let maxRow = 0
  let maxCol = 0
  let maxLayer = 0

  // 遍历所有库位记录，找出最大的行、列和层
  positionRows.forEach((position) => {
    const row = parseInt(position.Row) || 0
    const col = parseInt(position.List) || 0 // 在API中"List"对应列
    const layer = parseInt(position.Layer) || 0

    if (row > 0 && col > 0 && layer > 0) {
      // 确保有效值
      maxRow = Math.max(maxRow, row)
      maxCol = Math.max(maxCol, col)
      maxLayer = Math.max(maxLayer, layer)
    }
  })

  // 确保至少有默认值大小
  const oldRows = warehouseRows.value
  const oldCols = warehouseCols.value
  const oldLayers = warehouseLayers.value

  warehouseRows.value = Math.max(maxRow, 1) // 至少为1
  warehouseCols.value = Math.max(maxCol, 1) // 至少为1
  warehouseLayers.value = Math.max(maxLayer, 1) // 至少为1

  // 如果尺寸变化且选择器当前选择的值超出了新范围，则重置选择器
  if (currentView.value === 'front' && selectedIndex.value > warehouseRows.value) {
    selectedIndex.value = Math.min(selectedIndex.value, warehouseRows.value)
  } else if (currentView.value === 'side' && selectedIndex.value > warehouseCols.value) {
    selectedIndex.value = Math.min(selectedIndex.value, warehouseCols.value)
  } else if (currentView.value === 'top' && selectedIndex.value > warehouseLayers.value) {
    selectedIndex.value = Math.min(selectedIndex.value, warehouseLayers.value)
  }

  console.log(
    `仓库尺寸: ${warehouseRows.value}排 x ${warehouseCols.value}列 x ${warehouseLayers.value}层`
  )
}

// 处理API返回的库位数据
const processWarehouseData = (positionRows) => {
  // 初始化空数据结构
  const data = []
  for (let row = 1; row <= warehouseRows.value; row++) {
    const row_data = []
    for (let col = 1; col <= warehouseCols.value; col++) {
      const col_data = []
      for (let layer = 1; layer <= warehouseLayers.value; layer++) {
        col_data.push({
          id: `W${row}-${col}-${layer}`,
          row,
          col,
          layer,
          status: 'empty',
          GroupRow: 0,
          Depth: 0,
          PositionType: 0,
          palletCode: null,
          positionCode: null,
          positionId: null,
          warehouseId: null,
          zoneId: null,
          positionStatus: 0
        })
      }
      row_data.push(col_data)
    }
    data.push(row_data)
  }

  // 将API返回的库位数据填入数据结构
  positionRows.forEach((position) => {
    const row = parseInt(position.Row) || 0
    const col = parseInt(position.List) || 0 // 在API中"List"对应列
    const layer = parseInt(position.Layer) || 0

    // 确保索引在有效范围内
    if (
      row > 0 &&
      row <= warehouseRows.value &&
      col > 0 &&
      col <= warehouseCols.value &&
      layer > 0 &&
      layer <= warehouseLayers.value
    ) {
      // 获取库位状态
      let status = 'empty'
      if (position.PositionStatus === 3) {
        status = 'occupied' // 有货
      } else if (position.PositionStatus === 1) {
        // status = 'error'; // 异常
        status = 'unknown' //
      } else if (position.PositionStatus === 2) {
        // status = 'locked'; // 锁定
        status = 'unknown' //
      }

      // 更新库位信息
      const binData = data[row - 1][col - 1][layer - 1]
      binData.status = status
      binData.Depth = position.Depth || 0

      binData.GroupRow = position.GroupRow || 0
      binData.PositionType = position.PositionType || 0
      binData.palletCode = position.PalletCode || null
      binData.positionCode = position.PositionCode || null
      binData.positionId = position.PositionId || null
      binData.warehouseId = position.WarehouseId || null
      binData.zoneId = position.ZoneId || null
      binData.positionStatus = position.PositionStatus || 0
    }
  })

  warehouseData.value = data
}

// // 测试数据（API调用失败时使用）
// const generateWarehouseData = () => {
//   const data = []
//   const statuses = ['empty', 'occupied', 'locked', 'error', 'no']
//   for (let row = 1; row <= warehouseRows.value; row++) {
//     const row_data = []
//     for (let col = 1; col <= warehouseCols.value; col++) {
//       const col_data = []
//       for (let layer = 1; layer <= warehouseLayers.value; layer++) {
//         const status = statuses[Math.floor(Math.random() * statuses.length)]
//         col_data.push({
//           id: `W${row}-${col}-${layer}`,
//           row,
//           col,
//           layer,
//           status: status,
//           palletCode: status === 'occupied' ? `P${Math.floor(Math.random() * 9000) + 1000}` : null,
//           positionCode: `A-${row}-${col}-${layer}`,
//           positionId: null,
//           warehouseId: null,
//           zoneId: null,
//           positionStatus: getStatusNumber(status)
//         })
//       }
//       row_data.push(col_data)
//     }
//     data.push(row_data)
//   }
//   warehouseData.value = data
// }

onMounted(() => {
  fetchWarehouseList()
  fetchWarehouseData()
})

// --- 视图和选择器 ---
const currentView = ref('front') // 'front', 'side', 'top'
const selectedIndex = ref(1)
const loading = ref(false)

// 刷新数据
const refreshData = async () => {
  loading.value = true
  try {
    await fetchWarehouseData()
  } finally {
    loading.value = false
  }
}

const selectorLabel = computed(() => {
  switch (currentView.value) {
    case 'front':
      return '选择排 (Row)'
    case 'side':
      return '选择列 (Column)'
    case 'top':
      return '选择层 (Layer)'
    default:
      return '选择'
  }
})

const selectorOptions = computed(() => {
  let count = 0
  let labelPrefix = ''
  switch (currentView.value) {
    case 'front':
      count = warehouseRows.value
      labelPrefix = '第'
      return Array.from({ length: count }, (_, i) => ({
        value: i + 1,
        label: `${labelPrefix}${i + 1}排`
      }))
    case 'side':
      count = warehouseCols.value
      labelPrefix = '第'
      return Array.from({ length: count }, (_, i) => ({
        value: i + 1,
        label: `${labelPrefix}${i + 1}列`
      }))
    case 'top':
      count = warehouseLayers.value
      labelPrefix = '第'
      return Array.from({ length: count }, (_, i) => ({
        value: i + 1,
        label: `${labelPrefix}${i + 1}层`
      }))
    default:
      return []
  }
})

const viewChanged = () => {
  selectedIndex.value = 1
}

// --- 网格数据计算 ---
const currentBins = computed(() => {
  if (!warehouseData.value.length) return []

  const bins = []
  switch (currentView.value) {
    case 'front': // 正视图: x=列, y=层
      const row = selectedIndex.value - 1
      if (row >= 0 && row < warehouseData.value.length) {
        // 正视图中，按照列-层的顺序排列 (左到右，下到上)
        for (let col = 0; col < warehouseCols.value; col++) {
          if (warehouseData.value[row][col]) {
            // 层要倒序排列，让低层在下方显示
            for (let layer = warehouseLayers.value - 1; layer >= 0; layer--) {
              if (layer < warehouseLayers.value && warehouseData.value[row][col][layer]) {
                const bin = warehouseData.value[row][col][layer]
                bins.push({
                  ...bin,
                  label: `${bin.col}-${bin.layer}`,
                  displayLabel: `列${bin.col}-层${bin.layer}`,
                  gridCol: bin.col, // 列作为X坐标
                  gridRow: warehouseLayers.value - bin.layer + 1 // 反转层作为Y坐标，使层1在底部
                })
              }
            }
          }
        }
      }
      break
    case 'side': // 侧视图: x=排, y=层
      const col = selectedIndex.value - 1
      if (col >= 0 && col < warehouseCols.value) {
        // 侧视图中，按照排-层的顺序排列 (左到右，下到上)
        // 排序bins确保它们按照排和层正确排列
        let sideViewBins = []
        for (let row = 0; row < warehouseRows.value; row++) {
          if (warehouseData.value[row] && warehouseData.value[row][col]) {
            // 层要倒序排列，让低层在下方显示
            for (let layer = warehouseLayers.value - 1; layer >= 0; layer--) {
              if (layer < warehouseLayers.value && warehouseData.value[row][col][layer]) {
                const bin = warehouseData.value[row][col][layer]
                sideViewBins.push({
                  ...bin,
                  label: `${bin.row}-${bin.layer}`,
                  displayLabel: `排${bin.row}-层${bin.layer}`,
                  gridCol: bin.row, // 排作为X坐标
                  gridRow: warehouseLayers.value - bin.layer + 1 // 反转层作为Y坐标，使层1在底部
                })
              }
            }
          }
        }
        // 按网格位置排序
        sideViewBins.sort((a, b) => {
          if (a.gridRow === b.gridRow) {
            return a.gridCol - b.gridCol // 同一行，按列排序
          }
          return a.gridRow - b.gridRow // 先按行排序
        })
        bins.push(...sideViewBins)
      }
      break
    case 'top': // 俯视图: x=列, y=排
      const layer = selectedIndex.value - 1
      if (layer >= 0 && layer < warehouseLayers.value) {
        // 俯视图中，按照列-排的顺序排列
        for (let col = 0; col < warehouseCols.value; col++) {
          for (let row = 0; row < warehouseRows.value; row++) {
            if (
              warehouseData.value[row] &&
              warehouseData.value[row][col] &&
              warehouseData.value[row][col][layer]
            ) {
              const bin = warehouseData.value[row][col][layer]
              bins.push({
                ...bin,
                label: `${bin.row}-${bin.col}`,
                displayLabel: `排${bin.row}-列${bin.col}`,
                gridCol: bin.col, // 列作为X坐标
                gridRow: bin.row // 排作为Y坐标
              })
            }
          }
        }
      }
      break
  }
  return bins
})

const gridStyle = computed(() => {
  // 基础样式
  const style = {
    display: 'grid',
    gap: '4px',
    padding: '5px'
  }

  switch (currentView.value) {
    case 'front':
      // 正视图：列数 x 层数
      style.gridTemplateColumns = `repeat(${warehouseCols.value}, 1fr)`
      style.gridTemplateRows = `repeat(${warehouseLayers.value}, 1fr)`
      break
    case 'side':
      // 侧视图：排数 x 层数
      style.gridTemplateColumns = `repeat(${warehouseRows.value}, 1fr)`
      style.gridTemplateRows = `repeat(${warehouseLayers.value}, 1fr)`
      break
    case 'top':
      // 俯视图：列数 x 排数
      style.gridTemplateColumns = `repeat(${warehouseCols.value}, 1fr)`
      style.gridTemplateRows = `repeat(${warehouseRows.value}, 1fr)`
      break
  }

  return style
})

// --- 提示框 ---
const tooltip = reactive({
  visible: false,
  data: null,
  style: {}
})

// --- 详情对话框 ---
const detailDialog = reactive({
  visible: false,
  data: null,
  loading: false,
  mode: 'view' // 'view', 'edit'
})

// 打开详情对话框
const openDetailDialog = (bin) => {
  detailDialog.data = JSON.parse(JSON.stringify(bin)) // 深拷贝
  detailDialog.visible = true
  detailDialog.mode = 'view'
  hideTooltip() // 隐藏tooltip
}

// 切换编辑模式
const toggleEditMode = () => {
  detailDialog.mode = detailDialog.mode === 'view' ? 'edit' : 'view'
}

// 保存编辑
const saveChanges = async () => {
  detailDialog.loading = true
  try {
    // 调用API保存库位状态
    const params = {
      positionCode: detailDialog.data.positionCode,
      ZoneId: detailDialog.data.zoneId,
      positionType: detailDialog.data.PositionType,
      positionStatus: getStatusNumber(detailDialog.data.status)
    }

    const response = await http.post('/api/Wms_Position/UpdatePositionStatus', {  }, true,{params: params})

    if (response.Status === true) {
      // 更新本地数据
      const { row, col, layer } = detailDialog.data
      if (
        row > 0 &&
        row <= warehouseRows.value &&
        col > 0 &&
        col <= warehouseCols.value &&
        layer > 0 &&
        layer <= warehouseLayers.value
      ) {
        const binData = warehouseData.value[row - 1][col - 1][layer - 1]
        binData.status = detailDialog.data.status
        binData.PositionType = detailDialog.data.PositionType
        binData.positionStatus = getStatusNumber(detailDialog.data.status)
      }

      ElMessage({
        type: 'success',
        message: '库位信息已更新'
      })

      // 保存成功后刷新数据
      await refreshData()

      detailDialog.mode = 'view'
    } else {
      ElMessage({
        type: 'error',
        message: response.message || '保存库位信息失败'
      })
    }
  } catch (error) {
    console.error('保存库位信息失败:', error)
    ElMessage({
      type: 'error',
      message: '保存库位信息失败'
    })
  } finally {
    detailDialog.loading = false
  }
}

// 关闭详情对话框
const closeDetailDialog = () => {
  if (detailDialog.mode === 'edit') {
    ElMessageBox.confirm('您有未保存的更改，确定要关闭吗？', '确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        detailDialog.visible = false
      })
      .catch(() => {})
  } else {
    detailDialog.visible = false
  }
}

// 模拟库位锁定/解锁操作
const lockUnlockPosition = async () => {
  detailDialog.loading = true
  try {
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 更新状态
    const { row, col, layer } = detailDialog.data
    if (
      row > 0 &&
      row <= warehouseRows.value &&
      col > 0 &&
      col <= warehouseCols.value &&
      layer > 0 &&
      layer <= warehouseLayers.value
    ) {
      const binData = warehouseData.value[row - 1][col - 1][layer - 1]
      const newStatus = binData.status === 'locked' ? 'empty' : 'locked'
      binData.status = newStatus
      binData.positionStatus = getStatusNumber(newStatus)
      detailDialog.data.status = newStatus

      ElMessage({
        type: 'success',
        message: binData.status === 'locked' ? '库位已锁定' : '库位已解锁'
      })
    }
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage({
      type: 'error',
      message: '操作失败'
    })
  } finally {
    detailDialog.loading = false
  }
}

// 显示提示框
const showTooltip = (bin, event) => {
  tooltip.data = bin

  // 获取窗口宽度和高度
  const windowWidth = window.innerWidth
  const windowHeight = window.innerHeight

  // 设置默认偏移量
  let xOffset = 15
  let yOffset = 15

  // 根据鼠标位置智能调整提示框位置
  const rightSpace = windowWidth - event.clientX
  const bottomSpace = windowHeight - event.clientY

  // 估计tooltip的宽度和高度（根据实际情况调整）
  const tooltipWidth = 200 // 预估宽度
  const tooltipHeight = 180 // 预估高度

  // 如果右侧空间不足，则将提示框显示在左侧
  if (rightSpace < tooltipWidth + xOffset) {
    xOffset = -tooltipWidth - 15
  }

  // 如果下方空间不足，则将提示框显示在上方
  if (bottomSpace < tooltipHeight + yOffset) {
    yOffset = -tooltipHeight - 15
  }

  tooltip.style = {
    left: `${event.clientX + xOffset}px`,
    top: `${event.clientY + yOffset}px`
  }

  tooltip.visible = true
}

// 隐藏提示框
const hideTooltip = () => {
  tooltip.visible = false
}

const getStatusText = (status) => {
  const map = {
    empty: '空库位',
    occupied: '有货',
    locked: '锁定',
    error: '异常',
    unknown: '未知'
  }
  return map[status] || '未知'
}

// 获取状态值对应的数字
const getStatusNumber = (status) => {
  const map = {
    empty: 0,
    occupied: 3, // 根据需求，occupied 对应 PositionStatus=3
    locked: 1,
    error: 2
  }
  return map[status] !== undefined ? map[status] : 0
}

// 格式化库位状态数字为文本
const formatPositionStatus = (status) => {
  const map = {
    0: '空库位',
    1: '锁定',
    3: '有货', // PositionStatus=3 表示有货
    2: '异常'
  }
  return map[status] || '未知'
}
</script>

<template>
  <div class="warehouse-container">
    <el-card class="control-panel" shadow="never">
      <div class="control-header">
        <el-icon><OfficeBuilding /></el-icon>
        <span>库位视图控制</span>
        <div class="control-actions">
          <el-button size="small" type="primary" @click="refreshData" :loading="loading">
            <el-icon v-if="!loading"><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
      <!-- 第二行：选择框 -->
      <div style="display: flex; gap: 12px; align-items: center;">
          <div>
            <label style="font-size: 14px; color: #606266 ;">仓库：</label>
            <el-select 
              v-model="currentWarehouse"

              placeholder="请选择仓库" 
              style="width: 160px"
              @change="handleWarehouseChange"

            >
              <el-option 
                v-for="item in warehouseList" 
                :key="item.WarehouseId" 
                :label="item.Name" 
                :value="item.WarehouseId" 
              />
            </el-select>
          </div>

          <div>
            <label style="font-size: 14px; color: #606266 ;">库区：</label>
            <el-select 
              v-model="currentZone"

              placeholder="请选择库区" 
              style="width: 80px"
            >
              <el-option 
                v-for="item in zoneList" 
                :key="item.ZoneId" 
                :label="item.Name" 
                :value="item.ZoneId" 
              />
            </el-select>
          </div>
        </div>
      <el-form label-position="top">
        <el-form-item label="选择视图">
          <el-radio-group v-model="currentView" @change="viewChanged">
            <el-radio-button label="front">正视图</el-radio-button>
            <el-radio-button label="side">侧视图</el-radio-button>
            <el-radio-button label="top">俯视图</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="selectorLabel">
          <el-select v-model="selectedIndex" placeholder="请选择">
            <el-option
              v-for="item in selectorOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <!-- 仓库尺寸信息 -->
<!--      <div class="warehouse-info">-->
<!--        <div class="info-title">仓库尺寸</div>-->
<!--        <div class="info-content">-->
<!--          <div class="info-item">-->
<!--            <span class="info-label">排数:</span>-->
<!--            <span class="info-value">{{ warehouseRows.value }}</span>-->
<!--          </div>-->
<!--          <div class="info-item">-->
<!--            <span class="info-label">列数:</span>-->
<!--            <span class="info-value">{{ warehouseCols.value }}</span>-->
<!--          </div>-->
<!--          <div class="info-item">-->
<!--            <span class="info-label">层数:</span>-->
<!--            <span class="info-value">{{ warehouseLayers.value }}</span>-->
<!--          </div>-->
<!--        </div>-->
<!--      </div>-->

      <div class="legend">
        <span class="legend-item"><i class="legend-color empty"></i> 空库位</span>
        <span class="legend-item"><i class="legend-color occupied"></i> 有货</span>
        <span class="legend-item"><i class="legend-color locked"></i> 锁定</span>
        <span class="legend-item"><i class="legend-color error"></i> 异常</span>

      </div>
    </el-card>

    <el-card class="grid-view" shadow="never" v-loading="loading">
      <div class="view-title">
        <template v-if="currentView === 'front'"> 正视图 (第{{ selectedIndex }}排) </template>
        <template v-else-if="currentView === 'side'"> 侧视图 (第{{ selectedIndex }}列) </template>
        <template v-else-if="currentView === 'top'"> 俯视图 (第{{ selectedIndex }}层) </template>
      </div>
      <div class="grid-container" :style="gridStyle">
        <div
          v-for="bin in currentBins"
          :key="bin.id"
          class="grid-cell"
          :class="bin.status"
          :style="{
            gridColumn: `${bin.gridCol} / span 1`,
            gridRow: `${bin.gridRow} / span 1`
          }"
          @mouseenter="showTooltip(bin, $event)"
          @mouseleave="hideTooltip"
          @click="openDetailDialog(bin)"
        >
          <span class="cell-code">{{ bin.positionCode || bin.id }}</span>
          <span class="cell-label">{{ bin.displayLabel }}</span>
          <span v-if="bin.palletCode" class="cell-pallet">{{ bin.palletCode }}</span>
        </div>
      </div>
    </el-card>

    <div v-if="tooltip.visible" class="tooltip" :style="tooltip.style">
      <strong>{{ tooltip.data.positionCode || tooltip.data.id }}</strong
      ><br />
      <table class="tooltip-table">
        <tr>
          <td>仓位编码:</td>
          <td>{{ tooltip.data.positionCode || '无' }}</td>
        </tr>
        <tr>
          <td>托盘编码:</td>
          <td>{{ tooltip.data.palletCode || '无' }}</td>
        </tr>
        <tr>
          <td>仓位类型:</td>
          <td>
            {{ tooltip.data.PositionType === 1 ? '货位' : tooltip.data.PositionType || '未知' }}
          </td>
        </tr>
        <tr>
          <td>仓位状态:</td>
          <td>{{ formatPositionStatus(tooltip.data.positionStatus) }}</td>
        </tr>
        <tr>
          <td>分组:</td>
          <td>{{ tooltip.data.GroupRow || '无' }}</td>
        </tr>
        <tr>
          <td>深度:</td>
          <td>{{ tooltip.data.Depth || '无' }}</td>
        </tr>
        <tr>
          <td>位置:</td>
          <td>排{{ tooltip.data.row }}-列{{ tooltip.data.col }}-层{{ tooltip.data.layer }}</td>
        </tr>
      </table>
    </div>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialog.visible"
      :title="detailDialog.mode === 'view' ? '库位详情' : '编辑库位'"
      width="500px"
      :close-on-click-modal="false"
      :before-close="closeDetailDialog"
    >
      <div v-loading="detailDialog.loading">
        <template v-if="detailDialog.data">
          <div class="detail-header">
            <div class="position-id">
              {{ detailDialog.data.positionCode || detailDialog.data.id }}
            </div>
            <div :class="['status-badge', detailDialog.data.status]">
              {{ getStatusText(detailDialog.data.status) }}
            </div>
          </div>

          <div class="detail-content">
            <el-descriptions :column="1" border>
              <el-descriptions-item label="库位ID">{{
                detailDialog.data.positionId
              }}</el-descriptions-item>
              <el-descriptions-item label="仓库ID">{{
                detailDialog.data.warehouseId
              }}</el-descriptions-item>
              <el-descriptions-item label="区域ID">{{
                detailDialog.data.zoneId
              }}</el-descriptions-item>
              <el-descriptions-item label="位置">
                排{{ detailDialog.data.row }}-列{{ detailDialog.data.col }}-层{{
                  detailDialog.data.layer
                }}
              </el-descriptions-item>
              <el-descriptions-item label="组号">
                {{ detailDialog.data.GroupRow }}
              </el-descriptions-item>

              <el-descriptions-item label="深度">
                {{ detailDialog.data.Depth }}
              </el-descriptions-item>
              <el-descriptions-item label="托盘号">
                {{ detailDialog.data.palletCode || '无' }}
              </el-descriptions-item>

              <template v-if="detailDialog.mode === 'view'">
                <el-descriptions-item label="类型">
                  {{ detailDialog.data.PositionType === 1 ? '货位' : '未知' }}
                </el-descriptions-item>

                <el-descriptions-item label="状态">
                  {{ getStatusText(detailDialog.data.status) }}
                </el-descriptions-item>
              </template>

              <template v-else>
                <el-descriptions-item label="类型">
                  <el-select v-model="detailDialog.data.PositionType" placeholder="请选择类型">
                    <el-option label="货位" :value="1"></el-option>
                    <el-option label="未知" :value="0"></el-option>
                  </el-select>
                </el-descriptions-item>
                <el-descriptions-item label="状态">
                  <el-select v-model="detailDialog.data.status" placeholder="请选择状态">
                    <el-option label="空库位" value="empty"></el-option>
                    <el-option label="有货" value="occupied"></el-option>
                    <el-option label="锁定" value="locked"></el-option>
                    <el-option label="异常" value="error"></el-option>
                  </el-select>
                </el-descriptions-item>
              </template>
            </el-descriptions>
          </div>
        </template>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <template v-if="detailDialog.mode === 'view'">
            <el-button @click="closeDetailDialog">关闭</el-button>
            <el-button type="primary" @click="toggleEditMode">
              <el-icon><Edit /></el-icon>编辑
            </el-button>
            <el-button
              :type="
                detailDialog.data && detailDialog.data.status === 'locked' ? 'success' : 'warning'
              "
              @click="lockUnlockPosition"
            >
              <el-icon v-if="detailDialog.data && detailDialog.data.status === 'locked'">
                <Check />
              </el-icon>
              <el-icon v-else>
                <Close />
              </el-icon>
              {{ detailDialog.data && detailDialog.data.status === 'locked' ? '解锁' : '锁定' }}
            </el-button>
          </template>
          <template v-else>
            <el-button @click="toggleEditMode">取消</el-button>
            <el-button type="primary" @click="saveChanges" :loading="detailDialog.loading">
              保存
            </el-button>
          </template>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="less">
.warehouse-container {
  display: flex;
  height: 100%;
  width: 100%;
  background-color: #f0f2f5;
  padding: 16px;
  gap: 16px;
}

.control-panel {
  width: 280px;
  flex-shrink: 0;
}

.control-header {
  display: flex;

  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 20px;
}

.control-header .el-icon {
  margin-right: 8px;
  font-size: 20px;
}

.control-actions {
  margin-left: auto;
}

.grid-view {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.view-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
  color: #303133;
}

:deep(.el-card__body) {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.grid-container {
  flex-grow: 1;
  display: grid;
  gap: 4px;
  padding: 5px;
  background-color: #e4e7ed;
  border-radius: 4px;
  overflow: auto;
  grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
  grid-template-rows: repeat(auto-fill, minmax(60px, 1fr));
  justify-items: stretch;
  align-items: stretch;
  position: relative;
}

.grid-cell {
  background-color: #dcdfe6;
  border-radius: 3px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 12px;
  min-width: 60px;
  min-height: 60px;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  position: relative;
  padding: 4px;
}

.grid-cell:hover {
  transform: scale(1.1);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  z-index: 10;
}

.cell-code {
  font-size: 11px;
  font-weight: bold;
  margin-bottom: 2px;
}

.cell-label {
  opacity: 0.7;
  font-size: 10px;
}

.cell-pallet {
  font-size: 10px;
  margin-top: 2px;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 1px 3px;
  border-radius: 2px;
}

.grid-cell.empty {
  background-color: #909399;
}
.grid-cell.occupied {
  background-color: #409eff;
}
.grid-cell.locked {
  background-color: #e6a23c;
}
.grid-cell.error {
  background-color: #f56c6c;
}
.grid-cell.no {
  background-color: #67c23a;
}

.legend {
  margin-top: 20px;
  border-top: 1px solid #ebeef5;
  padding-top: 15px;
}
.legend-item {
  //justify-content: center;
  display: flex;
  margin-left: 40px;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}
.legend-color {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  margin-right: 8px;
  display: inline-block;
}
.legend-color.empty {
  background-color: #909399;
}
.legend-color.occupied {
  background-color: #409eff;
}
.legend-color.locked {
  background-color: #e6a23c;
}
.legend-color.error {
  background-color: #f56c6c;
}
.legend-color.no {
  background-color: #67c23a;
}

.tooltip {
  position: fixed;
  background-color: rgba(0, 0, 0, 0.75);
  color: #fff;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 13px;
  z-index: 1000;
  pointer-events: none;
  transition: opacity 0.2s;
  min-width: 180px;
  max-width: 220px;
  max-height: 300px;
  overflow: auto;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
}

.tooltip-table {
  width: 100%;
  border-spacing: 0;
  margin-top: 5px;

  td {
    padding: 2px 0;

    &:first-child {
      color: #e6e6e6;
      width: 60px;
    }
  }
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .position-id {
    font-size: 18px;
    font-weight: bold;
  }

  .status-badge {
    padding: 4px 12px;
    border-radius: 12px;
    color: white;
    font-size: 14px;

    &.empty {
      background-color: #909399;
    }
    &.occupied {
      background-color: #409eff;
    }
    &.locked {
      background-color: #e6a23c;
    }
    &.error {
      background-color: #f56c6c;
    }
    &.no {
      background-color: #67c23a;
    }
  }
}

.detail-content {
  margin-bottom: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.warehouse-info {
  margin-top: 20px;
  border-top: 1px solid #ebeef5;
  padding-top: 15px;
  margin-bottom: 15px;
}

.info-title {
  font-weight: 500;
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.info-label {
  color: #606266;
}

.info-value {
  font-weight: 500;
  color: #303133;
}
</style>