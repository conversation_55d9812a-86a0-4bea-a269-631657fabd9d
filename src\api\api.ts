import { request } from '@/utils/request';
import type { ApiResponse } from '@/utils/request';

// 定义用户相关接口类型
export interface LoginParams {
    userName: string;
    password: string;
    verificationCode?: string;
    UUID?: string;
}

export interface UserInfo {
    token: string;
    userName: string;
    userId: string;
    // 添加其他用户信息字段
}

export interface VerificationCodeResponse {
    imageBase64: string;
    UUID: string;
}

// 用户相关API
export const userApi = {
    // 用户登录
    login(data: LoginParams): Promise<ApiResponse<UserInfo>> {
        return request.postWithLoading('/api/user/login', data, '正在登录....');
    },

    // 获取验证码
    getVerificationCode(): Promise<ApiResponse<VerificationCodeResponse>> {
        return request.get('/api/user/getVerificationCode');
    },

    // 获取用户信息
    getUserInfo(): Promise<ApiResponse<UserInfo>> {
        return request.get('/api/user/info');
    },

    // 用户登出
    logout(): Promise<ApiResponse<any>> {
        return request.post('/api/user/logout');
    }
};

// 设备相关API
export const deviceApi = {
    // 获取PLC设备列表
    getPlcDevices(): Promise<ApiResponse<any[]>> {
        return request.get('/api/device/plc/list');
    },

    // 获取设备详情
    getDeviceDetail(deviceId: string): Promise<ApiResponse<any>> {
        return request.get(`/api/device/${deviceId}`);
    },

    // 更新设备状态
    updateDeviceStatus(deviceId: string, status: any): Promise<ApiResponse<any>> {
        return request.post(`/api/device/${deviceId}/status`, status);
    }
};

// 仓库相关API
export const warehouseApi = {
    // 获取仓库列表
    getWarehouseList(): Promise<ApiResponse<any[]>> {
        return request.get('/api/warehouse/list');
    },

    // 获取库区列表
    getZoneList(warehouseId: string): Promise<ApiResponse<any[]>> {
        return request.get(`/api/warehouse/${warehouseId}/zones`);
    },

    // 获取仓库详情
    getWarehouseDetail(warehouseId: string): Promise<ApiResponse<any>> {
        return request.get(`/api/warehouse/${warehouseId}`);
    }
};

// 工作站相关API
export const stationApi = {
    // 获取工作站列表
    getStationList(): Promise<ApiResponse<any[]>> {
        return request.get('/api/station/list');
    },

    // 获取工作站详情
    getStationDetail(stationId: string): Promise<ApiResponse<any>> {
        return request.get(`/api/station/${stationId}`);
    },

    // 更新工作站状态
    updateStationStatus(stationId: string, status: any): Promise<ApiResponse<any>> {
        return request.post(`/api/station/${stationId}/status`, status);
    }
};

// WMS任务相关API
export const wmsTaskApi = {
    // 获取任务列表
    getTaskList(params?: any): Promise<ApiResponse<any[]>> {
        return request.get('/api/wms/tasks', params);
    },

    // 获取任务详情
    getTaskDetail(taskId: string): Promise<ApiResponse<any>> {
        return request.get(`/api/wms/task/${taskId}`);
    },

    // 创建任务
    createTask(taskData: any): Promise<ApiResponse<any>> {
        return request.post('/api/wms/task', taskData);
    },

    // 更新任务状态
    updateTaskStatus(taskId: string, status: any): Promise<ApiResponse<any>> {
        return request.post(`/api/wms/task/${taskId}/status`, status);
    }
};

// 历史信息相关API
export const historyApi = {
    // 获取历史记录
    getHistoryList(params?: any): Promise<ApiResponse<any[]>> {
        return request.get('/api/history/list', params);
    },

    // 获取历史详情
    getHistoryDetail(historyId: string): Promise<ApiResponse<any>> {
        return request.get(`/api/history/${historyId}`);
    }
};

// 数据看板相关API
export const dashboardApi = {
    // 获取看板数据
    getDashboardData(): Promise<ApiResponse<any>> {
        return request.get('/api/dashboard/data');
    },

    // 获取统计数据
    getStatistics(params?: any): Promise<ApiResponse<any>> {
        return request.get('/api/dashboard/statistics', params);
    }
};

// 导出所有API
export default {
    userApi,
    deviceApi,
    warehouseApi,
    stationApi,
    wmsTaskApi,
    historyApi,
    dashboardApi
};
