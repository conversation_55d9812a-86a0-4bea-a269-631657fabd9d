import { defineStore } from 'pinia'
import http from '../api/http'

export const useStationStore = defineStore('station', {
  state: () => ({
    stationList: [],
    // currentStation: '',
    loading: false
  }),
  
  getters: {
    // 获取站台列表
    getStationList: (state) => state.stationList,
    
    // // 获取当前选中的站台
    // getCurrentStation: (state) => state.currentStation,
    
    // 根据 ID 获取站台信息
    getStationById: (state) => (id) => {
      return state.stationList.find(station => station.StationId === id) || null
    },
    
    // // 根据站台编号获取站台名称
    // getStationNameByNo: (state) => (stationNo) => {
    //   const station = state.stationList.find(station => station.No === stationNo || station.No === Number(stationNo))
    //   return station ? station.Name : '无'
    // },
    
    // 获取加载状态
    isLoading: (state) => state.loading
  },
  
  actions: {
    // 获取所有站台数据
    async fetchStationList() {
      this.loading = true
      try {
        const response = await http.post('/api/Wcs_Station/GetAllData', {}, true)
        if (response.status === 0 && Array.isArray(response.rows)) {
          this.stationList = response.rows
          // // 默认选中第一项
          // if (this.stationList.length > 0 && !this.currentStation) {
          //   this.currentStation = this.stationList[0].StationId
          // }
          return this.stationList
        } else {
          console.error('获取站台列表失败:', response)
          return []
        }
      } catch (error) {
        console.error('获取站台列表出错:', error)
        return []
      } finally {
        this.loading = false
      }
    },
    
    // // 设置当前站台
    // setCurrentStation(stationId) {
    //   this.currentStation = stationId
    // },
    
    // 通过 ID 查找站台
    findStationById(stationId) {
      return this.stationList.find(station => station.StationId === stationId)
    },
    
    // 通过名称查找站台
    findStationByName(name) {
      return this.stationList.find(station => station.Name === name)
    },
    
    // 通过类型查找站台
    findStationsByType(type) {
      return this.stationList.filter(station => station.Type === type)
    },
    
    // 通过站台编号获取站台名称
    getStationNameByNo(stationNo) {
      const station = this.stationList.find(station => station.No === stationNo || station.No === Number(stationNo))
      return station ? station.Name : '无'
    },
    
    // 重置站台状态
    resetStation() {
      this.currentStation = ''
    }
  }
}) 