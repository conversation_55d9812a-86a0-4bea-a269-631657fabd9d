import { defineStore } from 'pinia';
import { deviceApi } from '@/api/api';
import type { PlcDevice, Station, Template, DeviceState } from '@/types/store';

export const useDeviceStore = defineStore('device', {
  state: (): DeviceState => ({
    plcDevices: [],
    selectedPlcId: null,
    selectedStationId: null,
    loading: false
  }),
  
  getters: {
    // 获取PLC设备列表
    getPlcDevices: (state): PlcDevice[] => state.plcDevices,
    
    // 获取选中的PLC设备
    getSelectedPlc: (state): PlcDevice | null => {
      return state.plcDevices.find(device => device.Id === state.selectedPlcId) || null;
    },
    
    // 获取选中PLC的工作站列表
    getStationsOfSelectedPlc: (state): Station[] => {
      const plc = state.plcDevices.find(device => device.Id === state.selectedPlcId);
      return plc?.wcs_Stations || [];
    },

    // 获取选中的工作站
    getSelectedStation: (state): Station | null => {
      const plc = state.plcDevices.find(device => device.Id === state.selectedPlcId);
      if (!plc || !plc.wcs_Stations) return null;
      
      return plc.wcs_Stations.find(station => station.Id === state.selectedStationId) || null;
    },
    
    // 获取选中工作站的模板列表
    getTemplatesOfSelectedStation: (state): Template[] => {
      const plc = state.plcDevices.find(device => device.Id === state.selectedPlcId);
      if (!plc || !plc.wcs_Stations) return [];
      
      const station = plc.wcs_Stations.find(station => station.Id === state.selectedStationId);
      return station?.Templates || [];
    },
    
    // 获取加载状态
    isLoading: (state): boolean => state.loading,

    // 根据ID获取PLC设备
    getPlcById: (state) => (plcId: string): PlcDevice | null => {
      return state.plcDevices.find(device => device.Id === plcId) || null;
    },

    // 根据ID获取工作站
    getStationById: (state) => (stationId: string): Station | null => {
      for (const plc of state.plcDevices) {
        if (plc.wcs_Stations) {
          const station = plc.wcs_Stations.find(station => station.Id === stationId);
          if (station) return station;
        }
      }
      return null;
    }
  },
  
  actions: {
    // 获取PLC设备列表
    async fetchPlcDevices(): Promise<PlcDevice[]> {
      this.loading = true;
      try {
        const response = await deviceApi.getPlcDevices();
        if (response.Status === true && Array.isArray(response.Data)) {
          this.plcDevices = response.Data;

          // 默认选中第一个设备和工作站
          if (this.plcDevices.length > 0 && !this.selectedPlcId) {
            this.selectedPlcId = this.plcDevices[0].Id;
            
            const firstPlc = this.plcDevices[0];
            if (firstPlc.wcs_Stations && firstPlc.wcs_Stations.length > 0) {
              this.selectedStationId = firstPlc.wcs_Stations[0].Id;
            }
          }
          
          return this.plcDevices;
        } else {
          console.error('Failed to fetch PLC devices:', response);
          return [];
        }
      } catch (error) {
        console.error('Error fetching PLC devices:', error);
        return [];
      } finally {
        this.loading = false;
      }
    },
    
    // 设置选中的PLC设备
    setSelectedPlc(plcId: string): void {
      this.selectedPlcId = plcId;
      
      // 重置工作站选择，并选中第一个工作站
      this.selectedStationId = null;
      const plc = this.plcDevices.find(device => device.Id === plcId);
      if (plc?.wcs_Stations?.length && plc.wcs_Stations.length > 0) {
        this.selectedStationId = plc.wcs_Stations[0].Id;
      }
    },
    
    // 设置选中的工作站
    setSelectedStation(stationId: string): void {
      this.selectedStationId = stationId;
    },
    
    // 重置设备状态
    resetDeviceState(): void {
      this.selectedPlcId = null;
      this.selectedStationId = null;
    },

    // 根据PLC ID获取设备名称
    getPlcName(plcId: string): string {
      const plc = this.plcDevices.find(device => device.Id === plcId);
      return plc ? plc.Name : '无';
    },

    // 根据工作站ID获取工作站名称
    getStationName(stationId: string): string {
      for (const plc of this.plcDevices) {
        if (plc.wcs_Stations) {
          const station = plc.wcs_Stations.find(station => station.Id === stationId);
          if (station) return station.Name;
        }
      }
      return '无';
    },

    // 获取指定PLC的工作站列表
    getStationsByPlcId(plcId: string): Station[] {
      const plc = this.plcDevices.find(device => device.Id === plcId);
      return plc?.wcs_Stations || [];
    },

    // 重置所有状态
    resetState(): void {
      this.plcDevices = [];
      this.selectedPlcId = null;
      this.selectedStationId = null;
      this.loading = false;
    }
  }
});
