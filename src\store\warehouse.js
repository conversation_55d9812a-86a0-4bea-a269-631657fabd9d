import { defineStore } from 'pinia'
import http from '../api/http'

export const useWarehouseStore = defineStore('warehouse', {
  state: () => ({
    warehouseList: [],
    currentWarehouse: '',
    zoneList: [],
    currentZone: '',
    loading: false
  }),
  
  getters: {
  //   // 根据仓库ID获取仓库名称
  //   getWarehouseNameById: (state) => (warehouseId) => {
  //     const warehouse = state.warehouseList.find(item => item.WarehouseId === warehouseId)
  //     return warehouse ? warehouse.Name : '无'
  //   },
  //
  //   // 根据库区ID获取库区名称
  //   getZoneNameById: (state) => (zoneId) => {
  //     const zone = state.zoneList.find(item => item.ZoneId === zoneId)
  //     return zone ? zone.Name : '无'
  //   }
   },
  
  actions: {
    // 获取仓库列表
    async fetchWarehouseList() {
      this.loading = true
      try {
        const response = await http.post('/api/Wms_Warehouse/GetAllData', {}, true)
        if (response.status === 0 && Array.isArray(response.rows)) {
          this.warehouseList = response.rows
          // 默认选中第一项
          if (this.warehouseList.length > 0) {
            this.currentWarehouse = this.warehouseList[0].WarehouseId
            // 加载该仓库下的库区
            await this.fetchZoneList(this.currentWarehouse)
          }
        } else {
          console.error('获取仓库列表失败:', response)
        }
      } catch (error) {
        console.error('获取仓库列表出错:', error)
      } finally {
        this.loading = false
      }
    },
    
    // 获取库区列表
    async fetchZoneList(warehouseId) {
      this.loading = true
      try {
        const response = await http.post('/api/Wms_Zone/GetAllData', { WarehouseId: warehouseId }, true)
        if (response.status === 0 && Array.isArray(response.rows)) {
          this.zoneList = response.rows
          // 默认选中第一项
          if (this.zoneList.length > 0) {
            this.currentZone = this.zoneList[0].ZoneId
          } else {
            this.currentZone = ''
          }
        } else {
          console.error('获取库区列表失败:', response)
          this.zoneList = []
          this.currentZone = ''
        }
      } catch (error) {
        console.error('获取库区列表出错:', error)
        this.zoneList = []
        this.currentZone = ''
      } finally {
        this.loading = false
      }
    },
    
    // 监听仓库变化，加载对应库区
    async handleWarehouseChange() {
      if (this.currentWarehouse) {
        await this.fetchZoneList(this.currentWarehouse)
      } else {
        this.zoneList = []
        this.currentZone = ''
      }
    },

    // 设置当前仓库
    setCurrentWarehouse(warehouseId) {
      this.currentWarehouse = warehouseId
    },
    
    // 设置当前库区
    setCurrentZone(zoneId) {
      this.currentZone = zoneId
    },
    
    // 根据仓库ID获取仓库名称
    getWarehouseName(warehouseId) {
      const warehouse = this.warehouseList.find(item => item.WarehouseId === warehouseId)
      return warehouse ? warehouse.Name : '无'
    },
    
    // 根据库区ID获取库区名称
    getZoneName(zoneId) {
      const zone = this.zoneList.find(item => item.ZoneId === zoneId)
      return zone ? zone.Name : '无'
    },
    
    // 获取所有仓库数据（不触发加载状态）
    getAllWarehouses() {
      return this.warehouseList
    },
    
    // 获取所有库区数据（不触发加载状态）
    getAllZones() {
      return this.zoneList
    }
  }
}) 