<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/user'
import { userApi } from '@/api/api'

const router = useRouter()
const userStore = useUserStore()
const loading = ref(false)
const loginFormRef = ref(null)
const codeImgSrc = ref('')

// 获取全局属性
const appContext = getCurrentInstance().appContext
const $message = appContext.config.globalProperties.$message || ElMessage
const $ts = appContext.config.globalProperties.$ts || ((text) => Array.isArray(text) ? text.join('') : text)

const userInfo = reactive({
  userName: '',
  password: '',
  verificationCode: '',
  UUID: undefined
})

const rules = reactive({
  userName: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ],
  verificationCode: [
    { required: false, message: '请输入验证码', trigger: 'blur' }
  ]
})

// 获取验证码
const getVierificationCode = async () => {
  try {
    const response = await userApi.getVerificationCode()
    if (response.data) {
      codeImgSrc.value = 'data:image/png;base64,' + response.data.imageBase64
      userInfo.UUID = response.data.UUID
    }
  } catch (err) {
    console.error('获取验证码失败:', err)
    // 如果获取验证码失败，不显示验证码输入框
    codeImgSrc.value = ''
  }
}

// 尝试获取验证码
try {
  getVierificationCode()
} catch (error) {
  console.error('获取验证码出错:', error)
  codeImgSrc.value = ''
}

// 登录处理
const login = async () => {
  if (!userInfo.userName) return $message.error($ts(['请输入', '账号']))
  if (!userInfo.password) return $message.error($ts(['请输入', '密码']))
  
  // 如果显示了验证码但未输入，可以取消注释以下代码
  // if (codeImgSrc.value && !userInfo.verificationCode) {
  //   return $message.error($ts(['请输入', '验证码']))
  // }
  
  loading.value = true
  try {
    const success = await userStore.login(userInfo)

    if (success) {
      ElMessage.success('登录成功')
      // 跳转到storage_home页面
      router.push({
        name: 'storage_home'
      })
    } else {
      // 登录失败时刷新验证码
      if (codeImgSrc.value) {
        getVierificationCode()
      }
      $message.error('登录失败，请检查用户名和密码')
    }
  } catch (error) {
    console.error('登录出错:', error)
    $message.error('登录失败，请稍后重试')
    // 登录失败时刷新验证码
    if (codeImgSrc.value) {
      getVierificationCode()
    }
  } finally {
    loading.value = false
  }
}

// 按回车键登录
const loginPress = (e) => {
  if (e.keyCode === 13) {
    login()
  }
}
</script>

<template>
  <div class="login-dialog">
    <div class="logo-container">
      <img src="@/assets/imgs/白底logo.png" alt="Logo" class="logo-image">
    </div>
    
    <h2 class="login-title">用户登录</h2>
    
    <el-form ref="loginFormRef" :model="userInfo" :rules="rules" label-position="top" class="login-form" @keypress="loginPress">
      <el-form-item label="用户名" prop="userName">
        <el-input 
          v-model="userInfo.userName" 
          placeholder="请输入用户名" 
          prefix-icon="el-icon-user"
          size="large"
        ></el-input>
      </el-form-item>
      
      <el-form-item label="密码" prop="password">
        <el-input 
          v-model="userInfo.password" 
          type="password" 
          placeholder="请输入密码"
          prefix-icon="el-icon-lock"
          size="large"
          show-password
        ></el-input>
      </el-form-item>
      
      <el-form-item label="验证码" prop="verificationCode" v-if="codeImgSrc">
        <div class="verification-code-container">
          <el-input 
            v-model="userInfo.verificationCode" 
            placeholder="请输入验证码"
            size="large"
          ></el-input>
          <img 
            :src="codeImgSrc" 
            class="verification-code-img" 
            @click="getVierificationCode" 
            alt="验证码"
          />
        </div>
      </el-form-item>
      
      <el-form-item>
        <el-button 
          type="primary" 
          @click="login" 
          :loading="loading" 
          class="login-button"
        >
          <span class="button-text">登 录</span>
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<style scoped lang="less">
.login-dialog {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 400px;
  padding: 30px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  z-index: 100;
  text-align: center;
}

.logo-container {
  margin-bottom: 20px;
}

.logo-image {
  max-width: 180px;
  height: auto;
  margin-bottom: 10px;
}

.login-title {
  font-size: 24px;
  color: #303133;
  margin-bottom: 30px;
  font-weight: 500;
}

.login-form {
  text-align: left;
}

.login-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

.login-form :deep(.el-input__inner) {
  height: 45px;
  line-height: 45px;
  border-radius: 8px;
  border: 1px solid #dcdfe6;
  transition: all 0.3s;
}

.login-form :deep(.el-input__inner:focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.verification-code-container {
  display: flex;
  align-items: center;
}

.verification-code-img {
  height: 38px;
  margin-left: 10px;
  border-radius: 4px;
  cursor: pointer;
}

.login-button {
  width: 100%;
  height: 50px;
  border-radius: 8px;
  font-size: 16px;
  margin-top: 10px;
  background: linear-gradient(90deg, #3a8ee6, #5ca9ff);
  border: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.login-button:hover {
  background: linear-gradient(90deg, #5ca9ff, #3a8ee6);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
}

.login-button:active {
  transform: translateY(0);
}

.button-text {
  position: relative;
  z-index: 1;
  letter-spacing: 2px;
}

.login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.5s;
}

.login-button:hover::before {
  left: 100%;
}
</style>