<template>
  <div class="stacker-container">
    <el-card shadow="never">
       <template #header>
        <div class="card-header">
          <span>堆垛机状态监控</span>
          <div class="control-group">
          <el-button type="primary"  :icon="RefreshRight" @click="refreshData">刷新</el-button>
          <el-select v-model="selectedStationId" placeholder="选择工位" style="width: 180px;">
            <el-option 
              v-for="station in stations" 
              :key="station.Id" 
              :label="station.Name" 
              :value="station.Id" 
            />
          </el-select>
        </div>
      </div>
      </template>

      <div v-if="selectedStation">
        <el-descriptions :column="3" border>
          <el-descriptions-item label="PLC设备">{{ plcDevice && plcDevice.Name || '---' }}</el-descriptions-item>
          <el-descriptions-item label="堆垛机名称">{{ selectedStation.Name }}</el-descriptions-item>
          <el-descriptions-item label="堆垛机编号">{{ selectedStation.No }}</el-descriptions-item>
          
          <el-descriptions-item label="IP地址">{{ plcDevice && plcDevice.Ip || '---' }}</el-descriptions-item>
          <el-descriptions-item label="端口">{{ plcDevice && plcDevice.Port || '---' }}</el-descriptions-item>
          <el-descriptions-item label="PLC运行状态">
            <el-tag :type="plcDevice && plcDevice.Enabled === 1 ? statusMap['running'].type : statusMap['offline'].type" size="small">
              {{ plcDevice && plcDevice.Enabled === 1 ? statusMap['running'].text : statusMap['offline'].text }}
            </el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="运行状态"
                                v-if="stationData.runStatus !== undefined && stationData.runStatus !== null">
            {{ runStatusMap[stationData.runStatus]}}
          </el-descriptions-item>
          <el-descriptions-item label="是否自动远程"
                                v-if="stationData.isAutoOnline !== undefined && stationData.isAutoOnline !== null">
            <el-tag :type="stationData.isAutoOnline ? 'success' : 'info'" size="small">
              {{ stationData.isAutoOnline ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="当前排X"
                                v-if="stationData.nowX !== undefined && stationData.nowX !== null">
            {{ stationData.nowX|| '---'  }}
          </el-descriptions-item>
          <el-descriptions-item label="当前列Y"
                                v-if="stationData.nowY !== undefined && stationData.nowY !== null">
            {{ stationData.nowY|| '---'  }}
          </el-descriptions-item>
          <el-descriptions-item label="当前层Z"
                                v-if="stationData.nowZ !== undefined && stationData.nowZ !== null">
            {{ stationData.nowZ || '---'  }}
          </el-descriptions-item>

          <el-descriptions-item label="任务完成"
                                v-if="stationData.taskFinish !== undefined && stationData.taskFinish !== null">
            <el-tag :type="stationData.taskFinish ? 'success' : 'info'" size="small">
              {{ stationData.taskFinish ? '已完成' : '未完成' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="完成任务号"
                                v-if="stationData.finishNo !== undefined && stationData.finishNo !== null">
            {{ stationData.finishNo ||'---' }}
          </el-descriptions-item>
          <el-descriptions-item label="有无故障"
                                v-if="stationData.errorNo !== undefined && stationData.errorNo !== null">
            <el-tag :type="stationData.errorNo ? 'danger' : 'success'" size="small">
              {{ stationData.errorNo ===0? '无': '有' }}
            </el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="任务类型"
                                v-if="stationData.getTaskType !== undefined && stationData.getTaskType !== null">
            {{ stationData.getTaskType || '---' }}
          </el-descriptions-item>
          <el-descriptions-item label="起始排X"
                                v-if="stationData.getStartX !== undefined && stationData.getStartX !== null">
            {{ stationData.getStartX || '---' }}
          </el-descriptions-item>
          <el-descriptions-item label="起始列Y"
                                v-if="stationData.getStartX !== undefined && stationData.getStartX !== null">
            {{ stationData.getStartX || '---' }}
          </el-descriptions-item>

          <el-descriptions-item label="起始层Z"
                                v-if="stationData.getStartZ !== undefined && stationData.getStartZ !== null">
            {{ stationData.getStartZ || '---' }}
          </el-descriptions-item>
          <el-descriptions-item label="目标排X"
                                v-if="stationData.getEndX !== undefined && stationData.getEndX !== null">
            {{ stationData.getEndX || '---' }}
          </el-descriptions-item>
          <el-descriptions-item label="目标列Y"
                                v-if="stationData.getEndY !== undefined && stationData.getEndY !== null">
            {{ stationData.getEndY || '---' }}
          </el-descriptions-item>

          <el-descriptions-item label="目标层Z"
                                v-if="stationData.getEndZ !== undefined && stationData.getEndZ !== null">
            {{ stationData.getEndZ || '---' }}
          </el-descriptions-item>


        </el-descriptions>

<!--        <el-divider>消息</el-divider>-->
<!--        <div class="message-panel">-->
<!--          <el-scrollbar height="200px" ref="messageScrollbar">-->
<!--            <div v-for="(msg, index) in messages" -->
<!--                 :key="index" -->
<!--                 class="message-item" -->
<!--                 :class="{'new-message': msg.isNew, 'isAnimating': msg.isAnimating}">-->
<!--              <div class="message-time">{{ msg.time }}</div>-->
<!--              <div class="message-content">-->
<!--                <el-tag size="small" :type="msg.type">{{ msg.title }}</el-tag>-->
<!--                <span>{{ msg.content }}</span>-->
<!--              </div>-->
<!--            </div>-->
<!--            <div v-if="messages.length === 0" class="empty-message">-->
<!--              暂无消息-->
<!--            </div>-->
<!--          </el-scrollbar>-->
<!--        </div>-->

        <el-divider>设备控制</el-divider>
        <div class="control-buttons">
          <el-popconfirm title="确定要发送[任务启动]命令吗?" @confirm="sendCommand('Taskstart')">
            <template #reference>
              <el-button type="success" :icon="VideoPlay">任务启动</el-button>
            </template>
          </el-popconfirm>
           <el-popconfirm title="确定要发送[故障复位]命令吗?" @confirm="sendCommand('ErrorrRecover')">
            <template #reference>
              <el-button type="warning" :icon="Refresh">故障复位</el-button>
            </template>
          </el-popconfirm>
           <el-popconfirm title="确定要发送[WCS急停]命令吗?" @confirm="sendCommand('DStop')">
            <template #reference>
               <el-button type="danger" :icon="VideoPause">WCS急停</el-button>
            </template>
          </el-popconfirm>
          <el-button type="info" :icon="Edit" @click="openEditDialog">任务编辑</el-button>
        </div>

        <!-- 编辑对话框 -->
        <el-dialog
          v-model="editDialogVisible"
          title="任务编辑"
          width="500px"
          :close-on-click-modal="false"
        >
          <el-form :model="editForm" label-width="120px" :rules="editFormRules">
            <el-form-item label="堆垛机编号" prop="StackerNo">
              <el-input v-model="editForm.StackerNo" :placeholder="getDataTypeLabel('StackerNo')"></el-input>
            </el-form-item>
            <el-form-item label="任务类型" prop="TaskType">
              <el-select v-model="editForm.TaskType" placeholder="请选择任务类型" style="width: 100%">
                <el-option label="呼叫(9)" value="9" />
                <el-option label="入库通用(10)" value="10" />
                <el-option label="1号入库口入库(11)" value="11" />
                <el-option label="2号入库口入库(12)" value="12" />
                <el-option label="3号入库口入库(13)" value="13" />
                <el-option label="4号入库口入库(14)" value="14" />
                <el-option label="5号入库口入库(15)" value="15" />
                <el-option label="6号入库口入库(16)" value="16" />
                <el-option label="7号入库口入库(17)" value="17" />
                <el-option label="8号入库口入库(18)" value="18" />
                <el-option label="9号入库口入库(19)" value="19" />
                <el-option label="出库通用(20)" value="20" />
                <el-option label="1号出库口出库(21)" value="21" />
                <el-option label="2号出库口出库(22)" value="22" />
                <el-option label="3号出库口出库(23)" value="23" />
                <el-option label="4号出库口出库(24)" value="24" />
                <el-option label="5号出库口出库(25)" value="25" />
                <el-option label="6号出库口出库(26)" value="26" />
                <el-option label="7号出库口出库(27)" value="27" />
                <el-option label="8号出库口出库(28)" value="28" />
                <el-option label="9号出库口出库(29)" value="29" />
                <el-option label="移库(30)" value="30" />
              </el-select>
            </el-form-item>
            <el-form-item label="任务号" prop="WTaskNo">
              <el-input v-model="editForm.WTaskNo" :placeholder="getDataTypeLabel('WTaskNo')"></el-input>
            </el-form-item>
            <el-form-item label="取货排" prop="WgetX" :required="isSourceRequired">
              <el-input v-model="editForm.WgetX" :placeholder="getDataTypeLabel('WgetX')"></el-input>
            </el-form-item>
            <el-form-item label="取货列" prop="WgetY" :required="isSourceRequired">
              <el-input v-model="editForm.WgetY" :placeholder="getDataTypeLabel('WgetY')"></el-input>
            </el-form-item>
            <el-form-item label="取货层" prop="WgetZ" :required="isSourceRequired">
              <el-input v-model="editForm.WgetZ" :placeholder="getDataTypeLabel('WgetZ')"></el-input>
            </el-form-item>
            <el-form-item label="放货排" prop="WPutX" :required="isDestinationRequired">
              <el-input v-model="editForm.WPutX" :placeholder="getDataTypeLabel('WPutX')"></el-input>
            </el-form-item>
            <el-form-item label="放货列" prop="WPutY" :required="isDestinationRequired">
              <el-input v-model="editForm.WPutY" :placeholder="getDataTypeLabel('WPutY')"></el-input>
            </el-form-item>
            <el-form-item label="放货层" prop="WPutZ" :required="isDestinationRequired">
              <el-input v-model="editForm.WPutZ" :placeholder="getDataTypeLabel('WPutZ')"></el-input>
            </el-form-item>
          </el-form>
          <template #footer>
            <span class="dialog-footer">
              <el-button @click="editDialogVisible = false">取消</el-button>
              <el-button type="primary" @click="submitEditForm">确定</el-button>
            </span>
          </template>
        </el-dialog>
      </div>
       <el-empty v-else description="请先选择一个工位" />
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick, reactive } from 'vue';
import { ElNotification, ElMessage } from 'element-plus';
import { VideoPlay, VideoPause, Refresh, Edit, RefreshRight } from '@element-plus/icons-vue'
import { useRoute } from 'vue-router';
import { useDeviceStore } from '../../store/device';
import { request } from '@/utils/request'
import * as signalR from '@microsoft/signalr';

const route = useRoute();
const deviceStore = useDeviceStore();

const selectedStationId = ref(null);

// 消息列表
const messages = ref([]);
let connection = null;
const messageScrollbar = ref(null);

// 初始化SignalR连接
const initSignalR = async () => {
  try {
    // 获取当前登录用户信息
    const result = await http.post('api/user/GetCurrentUserInfo');
    if (result && result.data) {
      // 创建连接
      connection = new signalR.HubConnectionBuilder()
        .withAutomaticReconnect()
        .withUrl(`${http.ipAddress}wcsHub`)
        .build();

      // 启动连接
      await connection.start();
      console.log('SignalR连接成功');

      // 自动重连成功后的处理
      connection.onreconnected((connectionId) => {
        console.log('SignalR重新连接成功:', connectionId);
        addMessage('系统', '连接已恢复', 'success');
      });
      console.log('1-1');
      // 接收消息回调
      connection.on('StrackerMsg', (message) => {
        console.log('收到堆垛机消息:', message);
        addMessage('堆垛机消息', message, 'info');
      });
    }
  } catch (err) {
    console.error('SignalR连接失败:', err);
    addMessage('系统', '连接失败', 'danger');
  }
};

// 添加消息到列表
const addMessage = (title, content, type = 'info') => {
  const now = new Date();
  const timeStr = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
  
  // 先添加一个占位消息，用于动画效果
  const newMessage = {
    time: timeStr,
    title: title,
    content: content,
    type: type,
    isNew: true,
    isAnimating: true
  };
  
  messages.value.unshift(newMessage);
  
  // 最多保留50条消息
  if (messages.value.length > 50) {
    messages.value.pop();
  }
  
  // 滚动到顶部并在短暂延迟后移除新消息高亮
  nextTick(() => {
    if (messageScrollbar.value) {
      messageScrollbar.value.setScrollTop(0);
    }
    
    // 300ms后结束入场动画
    setTimeout(() => {
      if (messages.value.length > 0) {
        messages.value[0].isAnimating = false;
      }
    }, 300);
    
    // 2秒后移除新消息高亮
    setTimeout(() => {
      if (messages.value.length > 0) {
        messages.value[0].isNew = false;
      }
    }, 2000);
  });
};

// 获取当前选中的PLC设备
const plcDevice = computed(() => deviceStore.getSelectedPlc);

// 获取当前PLC设备下的所有工位
const stations = computed(() => deviceStore.getStationsOfSelectedPlc);

// 获取当前选中的工位
const selectedStation = computed(() => {
  if (!selectedStationId.value) return null;
  return stations.value.find(station => station.Id === selectedStationId.value);
});

const stationData = ref({
  stackerNo: '',
  runStatus: '',
  isAutoOnline: false,
  nowX: '',
  nowY: '',
  nowZ: '',
  taskFinish: '',
  finishNo: '',
  errorNo: '',
  getTaskType: '',
  getStartX: '',
  getStartY: '',
  getStartZ: '',
  getEndX: '',
  getEndY: '',
  getEndZ: '',
});

const fetchStationData = async () => {
  if (!selectedStation.value || !selectedStation.value.No) return;

  try {
    const res = await http.post(`/api/Wcs_PlcDataItem/ReadStationData?StationNo=${selectedStation.value.No}`, {});
    if (res && res.data) {
      stationData.value = res.data;
    }
  } catch (err) {
    console.error('获取工位数据失败:', err);

  }
};

// 数据类型映射
const dataTypeMap = {
  0: '未知',
  1: '短整型',
  2: '整型',
  3: '布尔值',
  4: '字符串'
};

const runStatusMap = {
  0: '无',
  1: "空闲",
  2: "去取货",
  3: "取货伸叉",
  4: "取货微升",
  5: "取货收叉",
  6: "去放货",
  7: "放货伸叉",
  8: "放货微降",
  9: "放货收叉",
  10: "任务完成",
  98: "维修或手动",
  99: "强制启动"
};

watch(() => selectedStation.value, (newStation) => {
  if (newStation) {
    fetchStationData();
  }
}, { immediate: true });

// 监听路由变化
watch(() => route.query.deviceId, (newDeviceId) => {
  if (newDeviceId && parseInt(newDeviceId)) {
    deviceStore.setSelectedPlc(parseInt(newDeviceId));
  } else {
    // 如果没有设备ID，尝试找到第一个堆垛机设备
    const stackerDevices = deviceStore.getPlcDevices.filter(device => device.Name.includes('堆垛机'));
    if (stackerDevices.length > 0) {
      deviceStore.setSelectedPlc(stackerDevices[0].Id);
    }
  }
  
  // 如果有工位，默认选择第一个
  if (stations.value.length > 0) {
    selectedStationId.value = stations.value[0].Id;
  }
}, { immediate: true });

// 监听工位列表变化
watch(() => stations.value, (newStations) => {
  if (newStations.length > 0 && !selectedStationId.value) {
    selectedStationId.value = newStations[0].Id;
  }
}, { immediate: true });

const statusMap = ref({
  running: { text: '运行中', type: 'success' },
  idle: { text: '空闲', type: 'primary' },
  error: { text: '故障', type: 'danger' },
  offline: { text: '离线', type: 'info' },
});

const sendCommand = async (command) => {
  if (!selectedStation.value || !selectedStation.value.No) {
    ElMessage.error('缺少工位号数据');
    return;
  }
  
  const url = `/api/Wcs_PlcDataItem/WritePlcData?StationNo=${selectedStation.value.No}&Key=${command}&value=1`;
  
  try {
    const res = await request.post(url, {}, { loading: true });
    if (res && res.status) {
      ElNotification({
        title: '命令已发送',
        message: `已向 ${selectedStation.value.Name} 发送 [${getCommandName(command)}] 命令。`,
        type: 'success',
      });
    } else {
      ElNotification({
        title: '命令发送失败',
        message: res.message || 'plc连接不上',
        type: 'error',
      });
    }
  } catch (err) {
    console.error('发送命令失败:', err);
    ElNotification({
      title: '命令发送失败',
      message: '请检查网络连接或服务器状态',
      type: 'error',
    });
  }
};

const getCommandName = (key) => {
  switch(key) {
    case 'Taskstart': return '任务启动';
    case 'ErrorrRecover': return '故障复位';
    case 'DStop': return 'WCS急停';
    default: return key;
  }
};

// 组件挂载时初始化SignalR连接
onMounted(() => {
  initSignalR();
});

// 组件销毁时清除定时器
onUnmounted(() => {
  // 关闭SignalR连接
  if (connection) {
    connection.stop();
  }
});

const editDialogVisible = ref(false);
const editFormRef = ref(null);
const editForm = reactive({
  StackerNo: '',
  TaskType: '',
  WTaskNo: '',
  WgetX: '',
  WgetY: '',
  WgetZ: '',
  WPutX: '',
  WPutY: '',
  WPutZ: ''
});

// 根据任务类型判断是否需要填写源位置信息
const isSourceRequired = computed(() => {
  const taskType = Number(editForm.TaskType);
  // 出库(20-29)和通用任务(10,20,30)需要填写源位置
  return taskType === 10 || taskType === 20 || taskType === 30 || 
         (taskType >= 20 && taskType <= 29);
});

// 根据任务类型判断是否需要填写目标位置信息
const isDestinationRequired = computed(() => {
  const taskType = Number(editForm.TaskType);
  // 呼叫(9)、入库(10-19)和通用任务(10,20,30)需要填写目标位置
  return taskType === 9 || taskType === 10 || taskType === 20 || taskType === 30 || 
         (taskType >= 10 && taskType <= 19);
});

const editFormRules = reactive({
  StackerNo: [
    { required: true, message: '请输入堆垛机编号', trigger: 'blur' }
  ],
  TaskType: [
    { required: true, message: '请输入任务类型', trigger: 'blur' }
  ],
  WTaskNo: [
    { required: true, message: '请输入任务号', trigger: 'blur' }
  ],
  WgetX: [
    { required: true, message: '请输入取货排', trigger: 'blur' }
  ],
  WgetY: [
    { required: true, message: '请输入取货列', trigger: 'blur' }
  ],
  WgetZ: [
    { required: true, message: '请输入取货层', trigger: 'blur' }
  ],
  WPutX: [
    { required: true, message: '请输入放货排', trigger: 'blur' }
  ],
  WPutY: [
    { required: true, message: '请输入放货列', trigger: 'blur' }
  ],
  WPutZ: [
    { required: true, message: '请输入放货层', trigger: 'blur' }
  ]
});

const openEditDialog = () => {
  if (!selectedStation.value || !selectedStation.value.No) {
    ElMessage.error('缺少工位号数据');
    return;
  }
  
  // 初始化表单数据
  editForm.StackerNo = selectedStation.value.No || '';
  editForm.TaskType = '';
  editForm.WTaskNo = '';
  editForm.WgetX = '';
  editForm.WgetY = '';
  editForm.WgetZ = '';
  editForm.WPutX = '';
  editForm.WPutY = '';
  editForm.WPutZ = '';
  
  editDialogVisible.value = true;
};

const getDataTypeLabel = (code) => {
  if (!selectedStation.value || !selectedStation.value.Templates) return '请输入数据';
  
  const template = selectedStation.value.Templates ? selectedStation.value.Templates.find(t => t.Code === code) : null;
  if (!template) return '请输入数据';
  
  const dataType = template.DataType;
  let label = '';
  if (dataType === 1 || dataType === 2) {
    label = '数字';
  } else if (dataType === 3) {
    label = '布尔值';
  } else if (dataType === 4) {
    label = '字符串';
  }
  return `请输入${label || '数据'}`;
};

const submitEditForm = async () => {
  const formEl = document.querySelector('form');
  if (!formEl) return;
  
  // 验证表单
  let isValid = true;
  
  // 验证必填字段
  if (!editForm.StackerNo || !editForm.TaskType || !editForm.WTaskNo) {
    isValid = false;
  }
  
  // 根据任务类型验证源位置和目标位置
  const taskType = Number(editForm.TaskType);
  
  // 验证源位置（取货位置）
  if (isSourceRequired.value && (!editForm.WgetX || !editForm.WgetY || !editForm.WgetZ)) {
    isValid = false;
  }
  
  // 验证目标位置（放货位置）
  if (isDestinationRequired.value && (!editForm.WPutX || !editForm.WPutY || !editForm.WPutZ)) {
    isValid = false;
  }
  
  if (isValid) {
    const stationNet = {
      StackerNo: editForm.StackerNo,
      TaskType: editForm.TaskType,
      WTaskNo: editForm.WTaskNo,
      WgetX: editForm.WgetX||0,
      WgetY: editForm.WgetY||0,
      WgetZ: editForm.WgetZ||0,
      WPutX: editForm.WPutX||0,
      WPutY: editForm.WPutY||0,
      WPutZ: editForm.WPutZ||0
    };
    
    try {
      const res = await http.post('/api/Wcs_PlcDataItem/StrackerTask', stationNet);
      if (res && res.status) {
        ElNotification({
          title: '提交成功',
          message: '堆垛机数据已更新',
          type: 'success'
        });
        editDialogVisible.value = false;
        // 刷新数据
         await fetchStationData();
      } else {
        ElNotification({
          title: '提交失败',
          message: res.message || 'plc连接不上',
          type: 'error'
        });
      }
    } catch (err) {
      console.error('提交堆垛机数据失败:', err);
      ElNotification({
        title: '提交失败',
        message: '请检查网络连接或服务器状态',
        type: 'error'
      });
    }
  } else {
    ElMessage.error('请填写所有必填项');
    return false;
  }
};

</script>
<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.control-buttons {
  display: flex;
  gap: 10px;
}
.stacker-visualization {
  margin: 20px 0;
}
.stacker-crane {
  height: 120px;
  background-color: #f0f2f5;
  border-radius: 4px;
  border: 2px solid #c0c4cc;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  position: relative;
}
.stacker-position {
  display: flex;
  gap: 20px;
  margin-bottom: 10px;
}
.stacker-position span {
  font-size: 16px;
  font-weight: bold;
  color: #606266;
}
.stacker-status {
  margin-top: 10px;
}
.message-panel {
  margin-top: 20px;
}
.message-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
  padding: 8px 5px;
  border-bottom: 1px solid #ebeef5;
  transition: all 0.5s ease;
  opacity: 1;
  transform: translateY(0);
}
.message-item.new-message {
  background-color: rgba(64, 158, 255, 0.1);
  animation: highlight-new-message 2s ease;
  border-left: 4px solid #409EFF;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}
.message-item.isAnimating {
  animation: slide-down 0.3s ease-out;
}
@keyframes slide-down {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes highlight-new-message {
  0% {
    background-color: rgba(64, 158, 255, 0.3);
    box-shadow: 0 2px 12px rgba(64, 158, 255, 0.3);
  }
  50% {
    background-color: rgba(64, 158, 255, 0.2);
    box-shadow: 0 2px 10px rgba(64, 158, 255, 0.2);
  }
  100% {
    background-color: rgba(64, 158, 255, 0.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  }
}
.message-time {
  font-size: 12px;
  color: #909399;
  min-width: 60px;
}
.message-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}
.empty-message {
  text-align: center;
  color: #909399;
  padding: 20px;
}
.control-group {
  display: flex;
  align-items: center;
}
</style> 