
import { createRouter, createWebHistory } from 'vue-router'
import redirect from './redirect'
import { useUserStore } from '@/store/user'

// 第二步：创建路由器
const router = createRouter({
    history:createWebHistory(), //路由器的工作模式（稍后讲解）
    routes:[
        {
            path:'/',
            name:'Home',
            component:()=>import('@/views/Home.vue'),
            meta: {
                keepAlive: false,
                anonymous: true
            },
            redirect: {
                name: 'storage_home'
            },
            children: [
                ...redirect,
                {
                    path: 'storage_home',
                    name: 'storage_home',
                    component: () => import('@/views/storage/Home.vue'),
                    meta: { anonymous: true }
                },
                {
                    path: 'storage_device',
                    name: 'storage_device',
                    component: () => import('@/views/storage/Device.vue'),
                    redirect: { name: 'device_overview' },
                    meta: { anonymous: true },
                    children: [
                        {
                            path: 'overview',
                            name: 'device_overview',
                            component: () => import('@/views/storage/DeviceOverview.vue'),
                            meta: { anonymous: true }
                        },
                        {
                            path: 'stacker',
                            name: 'device_stacker',
                            component: () => import('@/views/storage/StackerCrane.vue'),
                            meta: { anonymous: true }
                        },
                        {
                            path: 'conveyor',
                            name: 'device_conveyor',
                            component: () => import('@/views/storage/Conveyor.vue'),
                            meta: { anonymous: true }
                        }
                    ]
                },
                {
                    path: 'storage_history',
                    name: 'storage_history',
                    component: () => import('@/views/storage/History.vue'),
                    meta: { anonymous: true }
                },
                {
                    path: 'storage_WmsTask',
                    name: 'storage_WmsTask',
                    component: () => import('@/views/storage/WMSTask.vue'),
                    meta: { anonymous: true }
                }
            ],
            // 第一种写法：将路由收到的所有params参数作为props传给路由组件
            // props:true,
            // 第二种写法：函数写法，可以自己决定将什么作为props给路由组件
            // props(route){
            //     return route.query
            // }
        },
        {
            path: '/login',
            name: 'login',
            component: () => import('@/views/Login.vue'),
            meta: {
                anonymous: true
            }
        },
        {
            path: '/storage_Information',
            name: 'storage_Information',
            component: () => import('@/views/storage/Information.vue'),
            meta: {
                anonymous: true,
                fullScreen: true,
                keepAlive: false
            }
        }
    ]
})


router.beforeEach((to, from, next) => {
    // 1. 如果目标路由没有匹配到组件，跳转到 404 页面
    if (to.matched.length === 0) return next({ path: '/404' });

    // 获取用户store
    const userStore = useUserStore();

    // 3. 判断是否允许匿名访问 || 已登录 || 正在访问登录页
    if ((to.hasOwnProperty('meta') && to.meta.anonymous) || userStore.isLogin || to.path == '/login') {
        return next();
    }
    // 4. 其余情况跳转到登录页，并加上随机参数防止缓存
    next({ path: '/login', query: { redirect: Math.random() } });
})

router.afterEach((to, from) => {
    // 路由切换后的处理逻辑
})
router.onError((error) => {
    // const targetPath = router.currentRoute.value.matched;
    try {
        console.log(error.message);
        if (process.env.NODE_ENV === 'development') {
            alert(error.message)
        }
        localStorage.setItem("route_error", error.message)
    } catch (e) {

    }
    window.location.href = '/'
});

// 暴露出去router
export default router
