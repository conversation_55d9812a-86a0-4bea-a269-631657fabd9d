// pinia.js - 用于导出所有pinia store，便于统一管理

import { useWarehouseStore } from './warehouse';
import { useStationStore } from './station';
import { useDeviceStore } from './device';

// 导出所有pinia store
export {
  useWarehouseStore,
  useStationStore,
  useDeviceStore
};

// 辅助函数：获取所有store实例
export function usePiniaStores() {
  return {
    warehouse: useWarehouseStore(),
    station: useStationStore(),
    device: useDeviceStore()
  };
} 