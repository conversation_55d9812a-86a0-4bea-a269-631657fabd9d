import { defineStore } from 'pinia';
import { stationApi } from '@/api/api';
import type { StationInfo, StationState } from '@/types/store';

export const useStationStore = defineStore('station', {
  state: (): StationState => ({
    stationList: [],
    loading: false
  }),
  
  getters: {
    // 获取站台列表
    getStationList: (state): StationInfo[] => state.stationList,
    
    // 根据 ID 获取站台信息
    getStationById: (state) => (id: string): StationInfo | null => {
      return state.stationList.find(station => station.StationId === id) || null;
    },

    // 根据站台编号获取站台信息
    getStationByNo: (state) => (no: number): StationInfo | null => {
      return state.stationList.find(station => station.No === no) || null;
    },

    // 根据名称获取站台信息
    getStationByName: (state) => (name: string): StationInfo | null => {
      return state.stationList.find(station => station.Name === name) || null;
    },

    // 根据类型获取站台列表
    getStationsByType: (state) => (type: string): StationInfo[] => {
      return state.stationList.filter(station => station.Type === type);
    },

    // 根据站台编号获取站台名称
    getStationNameByNo: (state) => (stationNo: number): string => {
      const station = state.stationList.find(station => station.No === stationNo);
      return station ? station.Name : '无';
    },
    
    // 获取加载状态
    isLoading: (state): boolean => state.loading,

    // 获取可用的站台列表（状态为正常的）
    getAvailableStations: (state): StationInfo[] => {
      return state.stationList.filter(station => station.Status === 1);
    },

    // 获取站台总数
    getStationCount: (state): number => state.stationList.length
  },
  
  actions: {
    // 获取所有站台数据
    async fetchStationList(): Promise<StationInfo[]> {
      this.loading = true;
      try {
        const response = await stationApi.getStationList();
        if (response.status === 0 && Array.isArray(response.rows)) {
          this.stationList = response.rows;
          return this.stationList;
        } else {
          console.error('获取站台列表失败:', response);
          return [];
        }
      } catch (error) {
        console.error('获取站台列表出错:', error);
        return [];
      } finally {
        this.loading = false;
      }
    },
    
    // 通过 ID 查找站台
    findStationById(stationId: string): StationInfo | undefined {
      return this.stationList.find(station => station.StationId === stationId);
    },
    
    // 通过名称查找站台
    findStationByName(name: string): StationInfo | undefined {
      return this.stationList.find(station => station.Name === name);
    },
    
    // 通过类型查找站台
    findStationsByType(type: string): StationInfo[] {
      return this.stationList.filter(station => station.Type === type);
    },
    
    // 通过站台编号获取站台名称
    getStationNameByNo(stationNo: number): string {
      const station = this.stationList.find(station => station.No === stationNo);
      return station ? station.Name : '无';
    },

    // 添加站台
    addStation(station: StationInfo): void {
      this.stationList.push(station);
    },

    // 更新站台信息
    updateStation(stationId: string, updates: Partial<StationInfo>): boolean {
      const index = this.stationList.findIndex(station => station.StationId === stationId);
      if (index !== -1) {
        this.stationList[index] = { ...this.stationList[index], ...updates };
        return true;
      }
      return false;
    },

    // 删除站台
    removeStation(stationId: string): boolean {
      const index = this.stationList.findIndex(station => station.StationId === stationId);
      if (index !== -1) {
        this.stationList.splice(index, 1);
        return true;
      }
      return false;
    },

    // 根据ID获取站台名称
    getStationName(stationId: string): string {
      const station = this.stationList.find(station => station.StationId === stationId);
      return station ? station.Name : '无';
    },

    // 检查站台是否存在
    hasStation(stationId: string): boolean {
      return this.stationList.some(station => station.StationId === stationId);
    },
    
    // 重置站台状态
    resetState(): void {
      this.stationList = [];
      this.loading = false;
    }
  }
});
