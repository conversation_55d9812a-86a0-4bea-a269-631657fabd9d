// 仓库相关类型定义
export interface Warehouse {
  WarehouseId: string;
  Name: string;
  Code?: string;
  Description?: string;
  Status?: number;
  CreateTime?: string;
  UpdateTime?: string;
}

export interface Zone {
  ZoneId: string;
  WarehouseId: string;
  Name: string;
  Code?: string;
  Description?: string;
  Status?: number;
  CreateTime?: string;
  UpdateTime?: string;
}

export interface WarehouseState {
  warehouseList: Warehouse[];
  currentWarehouse: string;
  zoneList: Zone[];
  currentZone: string;
  loading: boolean;
}

// 设备相关类型定义
export interface Station {
  Id: string;
  Name: string;
  Code?: string;
  Type?: string;
  Status?: number;
  PlcId?: string;
  Templates?: Template[];
}

export interface Template {
  Id: string;
  Name: string;
  Code?: string;
  StationId?: string;
  Content?: string;
}

export interface PlcDevice {
  Id: string;
  Name: string;
  Code?: string;
  IpAddress?: string;
  Port?: number;
  Status?: number;
  wcs_Stations?: Station[];
}

export interface DeviceState {
  plcDevices: PlcDevice[];
  selectedPlcId: string | null;
  selectedStationId: string | null;
  loading: boolean;
}

// 工作站相关类型定义
export interface StationInfo {
  StationId: string;
  Name: string;
  No: number;
  Code?: string;
  Type?: string;
  Status?: number;
  Description?: string;
  CreateTime?: string;
  UpdateTime?: string;
}

export interface StationState {
  stationList: StationInfo[];
  loading: boolean;
}

// API响应类型
export interface ApiResponse<T = any> {
  status: number;
  rows?: T[];
  Data?: T;
  Status?: boolean;
  message?: string;
}

// 用户相关类型定义
export interface UserInfo {
  token: string;
  userName: string;
  userId: string;
  email?: string;
  phone?: string;
  avatar?: string;
  roles?: string[];
  permissions?: string[];
}

export interface UserState {
  userInfo: UserInfo | null;
  token: string;
  isLogin: boolean;
  permissions: string[];
}
