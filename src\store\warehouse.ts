import { defineStore } from 'pinia';
import { warehouseApi } from '@/api/api';
import type { Warehouse, Zone, WarehouseState, ApiResponse } from '@/types/store';

export const useWarehouseStore = defineStore('warehouse', {
  state: (): WarehouseState => ({
    warehouseList: [],
    currentWarehouse: '',
    zoneList: [],
    currentZone: '',
    loading: false
  }),
  
  getters: {
    // 根据仓库ID获取仓库名称
    getWarehouseNameById: (state) => (warehouseId: string): string => {
      const warehouse = state.warehouseList.find(item => item.WarehouseId === warehouseId);
      return warehouse ? warehouse.Name : '无';
    },

    // 根据库区ID获取库区名称
    getZoneNameById: (state) => (zoneId: string): string => {
      const zone = state.zoneList.find(item => item.ZoneId === zoneId);
      return zone ? zone.Name : '无';
    },

    // 获取当前仓库信息
    getCurrentWarehouse: (state): Warehouse | null => {
      return state.warehouseList.find(item => item.WarehouseId === state.currentWarehouse) || null;
    },

    // 获取当前库区信息
    getCurrentZone: (state): Zone | null => {
      return state.zoneList.find(item => item.ZoneId === state.currentZone) || null;
    },

    // 获取加载状态
    isLoading: (state): boolean => state.loading
  },
  
  actions: {
    // 获取仓库列表
    async fetchWarehouseList(): Promise<Warehouse[]> {
      this.loading = true;
      try {
        const response = await warehouseApi.getWarehouseList();
        if (response.status === 0 && Array.isArray(response.rows)) {
          this.warehouseList = response.rows;
          // 默认选中第一项
          if (this.warehouseList.length > 0) {
            this.currentWarehouse = this.warehouseList[0].WarehouseId;
            // 加载该仓库下的库区
            await this.fetchZoneList(this.currentWarehouse);
          }
          return this.warehouseList;
        } else {
          console.error('获取仓库列表失败:', response);
          return [];
        }
      } catch (error) {
        console.error('获取仓库列表出错:', error);
        return [];
      } finally {
        this.loading = false;
      }
    },
    
    // 获取库区列表
    async fetchZoneList(warehouseId: string): Promise<Zone[]> {
      this.loading = true;
      try {
        const response = await warehouseApi.getZoneList(warehouseId);
        if (response.status === 0 && Array.isArray(response.rows)) {
          this.zoneList = response.rows;
          // 默认选中第一项
          if (this.zoneList.length > 0) {
            this.currentZone = this.zoneList[0].ZoneId;
          } else {
            this.currentZone = '';
          }
          return this.zoneList;
        } else {
          console.error('获取库区列表失败:', response);
          this.zoneList = [];
          this.currentZone = '';
          return [];
        }
      } catch (error) {
        console.error('获取库区列表出错:', error);
        this.zoneList = [];
        this.currentZone = '';
        return [];
      } finally {
        this.loading = false;
      }
    },
    
    // 监听仓库变化，加载对应库区
    async handleWarehouseChange(): Promise<void> {
      if (this.currentWarehouse) {
        await this.fetchZoneList(this.currentWarehouse);
      } else {
        this.zoneList = [];
        this.currentZone = '';
      }
    },

    // 设置当前仓库
    setCurrentWarehouse(warehouseId: string): void {
      this.currentWarehouse = warehouseId;
    },
    
    // 设置当前库区
    setCurrentZone(zoneId: string): void {
      this.currentZone = zoneId;
    },
    
    // 根据仓库ID获取仓库名称
    getWarehouseName(warehouseId: string): string {
      const warehouse = this.warehouseList.find(item => item.WarehouseId === warehouseId);
      return warehouse ? warehouse.Name : '无';
    },
    
    // 根据库区ID获取库区名称
    getZoneName(zoneId: string): string {
      const zone = this.zoneList.find(item => item.ZoneId === zoneId);
      return zone ? zone.Name : '无';
    },
    
    // 获取所有仓库数据（不触发加载状态）
    getAllWarehouses(): Warehouse[] {
      return this.warehouseList;
    },
    
    // 获取所有库区数据（不触发加载状态）
    getAllZones(): Zone[] {
      return this.zoneList;
    },

    // 重置状态
    resetState(): void {
      this.warehouseList = [];
      this.currentWarehouse = '';
      this.zoneList = [];
      this.currentZone = '';
      this.loading = false;
    }
  }
});
