import { defineStore } from 'pinia'
import http from '../api/http'

export const useDeviceStore = defineStore('device', {
  state: () => ({
    plcDevices: [],
    selectedPlcId: null,
    selectedStationId: null,
    loading: false
  }),
  
  getters: {

    getPlcDevices: (state) => state.plcDevices,
    

    getSelectedPlc: (state) => {
      return state.plcDevices.find(device => device.Id === state.selectedPlcId) || null
    },
    

    getStationsOfSelectedPlc: (state) => {
      const plc = state.plcDevices.find(device => device.Id === state.selectedPlcId)
      return plc?.wcs_Stations || []
    },

    getSelectedStation: (state) => {
      const plc = state.plcDevices.find(device => device.Id === state.selectedPlcId)
      if (!plc || !plc.wcs_Stations) return null
      
      return plc.wcs_Stations.find(station => station.Id === state.selectedStationId) || null
    },
    

    getTemplatesOfSelectedStation: (state) => {
      const plc = state.plcDevices.find(device => device.Id === state.selectedPlcId)
      if (!plc || !plc.wcs_Stations) return []
      
      const station = plc.wcs_Stations.find(station => station.Id === state.selectedStationId)
      return station?.Templates || []
    },
    

    isLoading: (state) => state.loading
  },
  
  actions: {

    async fetchPlcDevices() {
      this.loading = true
      try {
        const response = await http.post('/api/Wcs_Hardware/getPlcDevices', {}, true)
        if (response.Status === true && Array.isArray(response.Data)) {
          this.plcDevices = response.Data

          if (this.plcDevices.length > 0 && !this.selectedPlcId) {
            this.selectedPlcId = this.plcDevices[0].Id
            

            const firstPlc = this.plcDevices[0]
            if (firstPlc.wcs_Stations && firstPlc.wcs_Stations.length > 0) {
              this.selectedStationId = firstPlc.wcs_Stations[0].Id
            }
          }
          
          return this.plcDevices
        } else {
          console.error('Failed to fetch PLC devices:', response)
          return []
        }
      } catch (error) {
        console.error('Error fetching PLC devices:', error)
        return []
      } finally {
        this.loading = false
      }
    },
    

    setSelectedPlc(plcId) {
      this.selectedPlcId = plcId
      

      this.selectedStationId = null
      const plc = this.plcDevices.find(device => device.Id === plcId)
      if (plc?.wcs_Stations?.length > 0) {
        this.selectedStationId = plc.wcs_Stations[0].Id
      }
    },
    

    setSelectedStation(stationId) {
      this.selectedStationId = stationId
    },
    

    resetDeviceState() {
      this.selectedPlcId = null
      this.selectedStationId = null
    }
  }
}) 