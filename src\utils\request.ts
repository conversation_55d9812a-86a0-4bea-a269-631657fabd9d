import axios from 'axios';
import type { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse,AxiosRequestConfig, AxiosError } from 'axios';
import { ElMessage } from 'element-plus';


// 创建Axios实例
const service: AxiosInstance = axios.create({
    baseURL: '',
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json; charset=utf-8'
    },
    // // 代理服务器配置
    // proxy:{
    //     protocol: 'https',
    //     host: '127.0.0.1',
    //     port: 8888,
    //     auth: {
    //         username: 'admin',
    //         password: 'password'
    //     }
    // },
    // //跨域时是否携带凭证
    // withCredentials: true,
})

// 请求拦截器
service.interceptors.request.use(
    (config:InternalAxiosRequestConfig) => {
        // 在发送请求之前做些什么
        // 加入 token、请求头等逻辑
        // 如果token存在localstorage，不用配置withCredentials
        // const token = localStorage.getItem('token')
        // if (token && config.headers) {
        //     config.headers['Authorization'] = `Bearer ${token}`
        // }
        return config
    },
    (error: AxiosError) => {
        // 对请求错误做些什么
        return Promise.reject(error)
    }
)

const handleError = (error: AxiosError) => {
    if (error.response) {
        // 服务器返回了错误状态码
        const status = error.response.status
        const data = error.response.data as any

        switch (status) {
            case 400:
                ElMessage.error(data.message || '请求参数错误')
                break
            case 401:
                ElMessage.error('未授权，请重新登录')
                break
            case 403:
                ElMessage.error('拒绝访问')
                break
            case 404:
                ElMessage.error('请求的资源不存在')
                break
            case 500:
                ElMessage.error(data.message || '服务器内部错误')
                break
            default:
                ElMessage.error(data.message || `请求错误 (${status})`)
        }
    } else if (error.request) {
        // 请求已发出，但没有收到响应
        if (error.message.includes('timeout')) {
            ElMessage.error('请求超时，请重试')
        } else {
            ElMessage.error('网络错误，请检查您的网络连接')
        }
    } else {
        // 在设置请求时发生错误
        ElMessage.error(error.message || '请求配置错误')
    }

    return Promise.reject(error)
}

// 响应拦截器
service.interceptors.response.use(
    (response : AxiosResponse) => {
        // 对响应数据做点什么
        return response.data
    },
    async (error: AxiosError) => {
        // 对响应错误做点什么
        const config = error.config as any

        // ✅ 网络或 5xx 错误支持重试
        if (config && config.retry > 0 && (
            !error.response || (error.response.status >= 500 && error.response.status < 600)
        )) {
            config.retry -= 1
            config.__retryCount = (config.__retryCount || 0) + 1

            // 创建新的Promise用于延迟
            const backoff = new Promise<void>(resolve => {
                setTimeout(() => {
                    console.log(`重试请求: ${config.url}, 尝试次数: ${config.__retryCount}`)
                    resolve()
                }, config.retryDelay || 1000)
            })

            // 返回重试请求
            return backoff.then(() => service(config))
        }

        return handleError(error)
    }
)

// 通用请求方法，增加类型安全
export const request = {
    get<T = any>(url: string, params?: any, config?: AxiosRequestConfig): Promise<T> {
        return service.get(url, { params, ...config })
    },
    post<T = any>(url: string, data?: any, config?: AxiosRequestConfig):  Promise<T>{
        return service.post(url, data, config)
    },
    put<T = any>(url: string, data?: any, config?: AxiosRequestConfig):  Promise<T>{
        return service.put(url, data, config)
    },
    delete<T = any>(url: string, config?: AxiosRequestConfig):  Promise<T> {
        return service.delete(url, config)
    }
}