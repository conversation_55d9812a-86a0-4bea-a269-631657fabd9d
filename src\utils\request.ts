import axios from 'axios';
import type { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse, AxiosRequestConfig, AxiosError } from 'axios';
import { ElMessage, ElLoading } from 'element-plus';
import { nextTick } from 'vue';

// 定义API响应类型
export interface ApiResponse<T = any> {
    data: T;
    message?: string;
    status?: boolean;
    code?: number;
}

// 定义请求配置类型
export interface RequestConfig extends AxiosRequestConfig {
    loading?: boolean | string;
    retry?: number;
    retryDelay?: number;
}

// 环境配置
const getBaseURL = (): string => {
    const env = import.meta.env.MODE;
    switch (env) {
        case 'development':
            return 'http://192.168.110.156:9100';
        case 'debug':
            return 'http://10.0.2.184:9001/';
        case 'production':
            return '/';
        default:
            return '/';
    }
};

// 创建Axios实例
const service: AxiosInstance = axios.create({
    baseURL: getBaseURL(),
    timeout: 50000,
    headers: {
        'Content-Type': 'application/json;charset=UTF-8'
    }
});

// Loading实例管理
let loadingInstance: any = null;
let loadingStatus = false;

// 显示Loading
const showLoading = (loading?: boolean | string) => {
    if (!loading || loadingStatus) {
        return;
    }
    loadingStatus = true;
    nextTick(() => {
        loadingInstance = ElLoading.service({
            lock: true,
            body: false,
            target: '.dashboard-content',
            text: typeof loading === 'string' ? loading : '正在处理.....',
            background: 'rgba(58, 61, 63, 0.32)'
        });
    });
};

// 关闭Loading
const closeLoading = () => {
    if (loadingInstance) {
        loadingInstance.close();
        loadingInstance = null;
    }
    loadingStatus = false;
};

// 获取Token
const getToken = (): string => {
    const userInfo = localStorage.getItem('user');
    if (userInfo) {
        try {
            const user = JSON.parse(userInfo);
            return user.token || '';
        } catch {
            return '';
        }
    }
    return '';
};

// 设置语言头
const setHeaderLang = (headers: any) => {
    const langType = localStorage.getItem('lang') || 'zh-CN';
    headers['lang'] = langType;
};

// 请求拦截器
service.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
        // 设置token
        const token = getToken();
        if (token && config.headers) {
            config.headers['Authorization'] = token;
        }

        // 设置其他头信息
        if (config.headers) {
            config.headers['serviceId'] = localStorage.getItem('serviceId') || '';
            config.headers['deptId'] = localStorage.getItem('deptId') || '';
            setHeaderLang(config.headers);
        }

        // 显示loading
        const customConfig = config as RequestConfig;
        if (customConfig.loading) {
            showLoading(customConfig.loading);
        }

        return config;
    },
    (error: AxiosError) => {
        closeLoading();
        return Promise.reject(error);
    }
);

// 处理登录跳转
const toLogin = () => {
    // 清除本地存储
    localStorage.removeItem('user');
    localStorage.removeItem('token');

    // 跳转到登录页
    if (window.location.pathname !== '/login') {
        window.location.href = '/login';
    }
};

// 错误处理
const handleError = (error: AxiosError) => {
    closeLoading();

    if (error.response) {
        const status = error.response.status;
        const data = error.response.data as any;

        switch (status) {
            case 400:
                ElMessage.error(data.message || '请求参数错误');
                break;
            case 401:
                ElMessage.error('登录已过期，请重新登录');
                toLogin();
                break;
            case 403:
                ElMessage.error('拒绝访问');
                break;
            case 404:
                ElMessage.error('未找到请求地址');
                break;
            case 500:
                ElMessage.error(data.message || '服务器内部错误');
                break;
            default:
                ElMessage.error(data.message || `请求错误 (${status})`);
        }
    } else if (error.request) {
        if (error.message.includes('timeout')) {
            ElMessage.error('请求超时，请重试');
        } else {
            ElMessage.error('网络错误，请检查您的网络连接');
        }
    } else {
        ElMessage.error(error.message || '请求配置错误');
    }

    return Promise.reject(error);
};

// 检查token刷新
const checkResponse = (response: AxiosResponse) => {
    if (response.headers?.vol_exp === '1') {
        // 处理token刷新逻辑
        console.log('Token需要刷新');
    }
};

// 响应拦截器
service.interceptors.response.use(
    (response: AxiosResponse) => {
        closeLoading();
        checkResponse(response);
        return response.data;
    },
    async (error: AxiosError) => {
        const config = error.config as RequestConfig;

        // 网络或 5xx 错误支持重试
        if (config && config.retry && config.retry > 0 && (
            !error.response || (error.response.status >= 500 && error.response.status < 600)
        )) {
            config.retry -= 1;
            config.__retryCount = (config.__retryCount || 0) + 1;

            // 创建新的Promise用于延迟
            const backoff = new Promise<void>(resolve => {
                setTimeout(() => {
                    console.log(`重试请求: ${config.url}, 尝试次数: ${config.__retryCount}`);
                    resolve();
                }, config.retryDelay || 1000);
            });

            // 返回重试请求
            return backoff.then(() => service(config));
        }

        return handleError(error);
    }
);

// 通用请求方法，增加类型安全和loading支持
export const request = {
    get<T = any>(url: string, params?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
        return service.get(url, { params, ...config });
    },

    post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
        return service.post(url, data, config);
    },

    put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
        return service.put(url, data, config);
    },

    delete<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>> {
        return service.delete(url, config);
    },

    // 兼容旧版本的方法
    postWithLoading<T = any>(url: string, data?: any, loading?: boolean | string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
        const requestConfig: RequestConfig = {
            ...config,
            loading
        };
        return service.post(url, data, requestConfig);
    },

    getWithLoading<T = any>(url: string, params?: any, loading?: boolean | string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
        const requestConfig: RequestConfig = {
            ...config,
            loading,
            params
        };
        return service.get(url, requestConfig);
    }
};

// 导出axios实例，供特殊需求使用
export { service as axiosInstance };

// 导出默认实例
export default request;