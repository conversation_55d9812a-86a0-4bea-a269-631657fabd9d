import { defineStore } from 'pinia';
import { userApi } from '@/api/api';
import type { UserInfo, UserState } from '@/types/store';

const USER_STORAGE_KEY = 'user';
const TOKEN_STORAGE_KEY = 'token';

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    userInfo: null,
    token: '',
    isLogin: false,
    permissions: []
  }),

  getters: {
    // 获取用户信息
    getUserInfo: (state): UserInfo | null => {
      if (state.userInfo) return state.userInfo;
      
      const userInfo = localStorage.getItem(USER_STORAGE_KEY);
      if (userInfo) {
        try {
          state.userInfo = JSON.parse(userInfo);
          return state.userInfo;
        } catch (error) {
          console.error('解析用户信息失败:', error);
          localStorage.removeItem(USER_STORAGE_KEY);
        }
      }
      return null;
    },

    // 获取Token
    getToken: (state): string => {
      if (state.token) return state.token;
      
      const token = localStorage.getItem(TOKEN_STORAGE_KEY);
      if (token) {
        state.token = token;
        return token;
      }
      
      // 从用户信息中获取token（兼容旧版本）
      const userInfo = localStorage.getItem(USER_STORAGE_KEY);
      if (userInfo) {
        try {
          const user = JSON.parse(userInfo);
          if (user.token) {
            state.token = user.token;
            return user.token;
          }
        } catch (error) {
          console.error('解析用户token失败:', error);
        }
      }
      
      return '';
    },

    // 检查是否已登录
    isLogin: (state): boolean => {
      return !!state.token || !!localStorage.getItem(TOKEN_STORAGE_KEY) || !!localStorage.getItem(USER_STORAGE_KEY);
    },

    // 获取用户名
    getUserName: (state): string => {
      const userInfo = state.userInfo || JSON.parse(localStorage.getItem(USER_STORAGE_KEY) || '{}');
      return userInfo?.userName || '';
    },

    // 获取用户ID
    getUserId: (state): string => {
      const userInfo = state.userInfo || JSON.parse(localStorage.getItem(USER_STORAGE_KEY) || '{}');
      return userInfo?.userId || '';
    },

    // 获取用户权限
    getPermissions: (state): string[] => {
      return state.permissions;
    },

    // 检查是否有指定权限
    hasPermission: (state) => (permission: string): boolean => {
      return state.permissions.includes(permission);
    }
  },

  actions: {
    // 设置用户信息
    setUserInfo(userInfo: UserInfo): void {
      this.userInfo = userInfo;
      this.token = userInfo.token;
      this.isLogin = true;
      this.permissions = userInfo.permissions || [];
      
      // 保存到本地存储
      localStorage.setItem(USER_STORAGE_KEY, JSON.stringify(userInfo));
      localStorage.setItem(TOKEN_STORAGE_KEY, userInfo.token);
      
      // 设置其他相关信息
      if (userInfo.userId) {
        localStorage.setItem('serviceId', userInfo.userId);
      }
    },

    // 设置Token
    setToken(token: string): void {
      this.token = token;
      localStorage.setItem(TOKEN_STORAGE_KEY, token);
    },

    // 设置权限
    setPermissions(permissions: string[]): void {
      this.permissions = permissions;
    },

    // 用户登录
    async login(loginData: { userName: string; password: string; verificationCode?: string; UUID?: string }): Promise<boolean> {
      try {
        const response = await userApi.login(loginData);
        if (response.status && response.data) {
          this.setUserInfo(response.data);
          return true;
        } else {
          console.error('登录失败:', response.message);
          return false;
        }
      } catch (error) {
        console.error('登录出错:', error);
        return false;
      }
    },

    // 用户登出
    async logout(): Promise<void> {
      try {
        await userApi.logout();
      } catch (error) {
        console.error('登出请求失败:', error);
      } finally {
        this.clearUserInfo();
      }
    },

    // 清除用户信息
    clearUserInfo(): void {
      this.userInfo = null;
      this.token = '';
      this.isLogin = false;
      this.permissions = [];
      
      // 清除本地存储
      localStorage.removeItem(USER_STORAGE_KEY);
      localStorage.removeItem(TOKEN_STORAGE_KEY);
      localStorage.removeItem('serviceId');
      localStorage.removeItem('deptId');
    },

    // 刷新用户信息
    async refreshUserInfo(): Promise<boolean> {
      try {
        const response = await userApi.getUserInfo();
        if (response.status && response.data) {
          this.setUserInfo(response.data);
          return true;
        }
        return false;
      } catch (error) {
        console.error('刷新用户信息失败:', error);
        return false;
      }
    },

    // 初始化用户状态（从本地存储恢复）
    initUserState(): void {
      const userInfo = localStorage.getItem(USER_STORAGE_KEY);
      const token = localStorage.getItem(TOKEN_STORAGE_KEY);
      
      if (userInfo) {
        try {
          const user = JSON.parse(userInfo);
          this.userInfo = user;
          this.permissions = user.permissions || [];
          this.isLogin = true;
        } catch (error) {
          console.error('初始化用户信息失败:', error);
          this.clearUserInfo();
        }
      }
      
      if (token) {
        this.token = token;
      }
    },

    // 检查登录状态
    checkLoginStatus(): boolean {
      const token = this.getToken;
      const userInfo = this.getUserInfo;
      
      if (token && userInfo) {
        this.isLogin = true;
        return true;
      } else {
        this.isLogin = false;
        return false;
      }
    }
  }
});
