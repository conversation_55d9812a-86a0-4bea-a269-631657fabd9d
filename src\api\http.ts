// 兼容性文件 - 为了保持向后兼容，导出旧的API接口
import { request } from '@/utils/request';

// 兼容旧版本的http对象
const http = {
  // POST请求
  post(url: string, data?: any, loading?: boolean | string, config?: any) {
    return request.postWithLoading(url, data, loading, config);
  },

  // GET请求
  get(url: string, params?: any, loading?: boolean | string, config?: any) {
    return request.getWithLoading(url, params, loading, config);
  },

  // PUT请求
  put(url: string, data?: any, loading?: boolean | string, config?: any) {
    const requestConfig = {
      ...config,
      loading
    };
    return request.put(url, data, requestConfig);
  },

  // DELETE请求
  delete(url: string, loading?: boolean | string, config?: any) {
    const requestConfig = {
      ...config,
      loading
    };
    return request.delete(url, requestConfig);
  },

  // 获取IP地址（兼容性）
  get ipAddress() {
    const env = import.meta.env.MODE;
    switch (env) {
      case 'development':
        return 'http://***************:9100/';
      case 'debug':
        return 'http://**********:9001/';
      case 'production':
        return '/';
      default:
        return '/';
    }
  }
};

export default http;
