# WCS仓储管理系统

基于 Vue 3 + TypeScript + ElementPlus + Pinia + Vite 构建的现代化仓储管理系统。

## 技术栈

- **前端框架**: Vue 3.5.13
- **开发语言**: TypeScript 5.8.3
- **构建工具**: Vite 6.3.5
- **UI组件库**: Element Plus 2.10.1
- **状态管理**: Pinia 3.0.3
- **路由管理**: Vue Router 4.5.1
- **HTTP客户端**: Axios 1.9.0

## 项目结构

```
src/
├── api/                    # API接口定义
│   ├── api.ts             # 新的API模块化接口
│   ├── http.ts            # 兼容性HTTP工具
│   └── index.ts           # 旧版API（待废弃）
├── assets/                # 静态资源
├── components/            # 公共组件
├── hooks/                 # 自定义Hooks
├── router/                # 路由配置
├── store/                 # Pinia状态管理
│   ├── user.ts           # 用户状态
│   ├── device.ts         # 设备状态
│   ├── warehouse.ts      # 仓库状态
│   ├── station.ts        # 工作站状态
│   └── pinia.ts          # Store统一导出
├── types/                 # TypeScript类型定义
├── utils/                 # 工具函数
│   └── request.ts        # HTTP请求工具
├── views/                 # 页面组件
└── main.ts               # 应用入口
```

## 开发指南

### 安装依赖

```bash
npm install
```

### 开发环境启动

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 类型检查

```bash
npm run type-check
```

### 预览生产构建

```bash
npm run preview
```

## 环境配置

项目支持多环境配置：

- `.env.development` - 开发环境
- `.env.production` - 生产环境

### 环境变量

- `VITE_API_BASE_URL` - API基础URL
- `VITE_DATA_VIEW_URL` - 数据看板URL
- `VITE_APP_TITLE` - 应用标题
- `VITE_APP_VERSION` - 应用版本

## API使用

### 新版API（推荐）

```typescript
import { userApi, deviceApi } from '@/api/api'

// 用户登录
const result = await userApi.login(loginData)

// 获取设备列表
const devices = await deviceApi.getPlcDevices()
```

### HTTP请求工具

```typescript
import { request } from '@/utils/request'

// 带loading的请求
const data = await request.postWithLoading('/api/data', params, '加载中...')

// 普通请求
const result = await request.post('/api/data', params)
```

## 状态管理

使用Pinia进行状态管理：

```typescript
import { useUserStore, useDeviceStore } from '@/store/pinia'

const userStore = useUserStore()
const deviceStore = useDeviceStore()

// 用户操作
await userStore.login(loginData)
const isLogin = userStore.isLogin

// 设备操作
await deviceStore.fetchPlcDevices()
const devices = deviceStore.getPlcDevices
```

## 特性

- ✅ Vue 3 Composition API
- ✅ TypeScript 类型安全
- ✅ Pinia 状态管理
- ✅ Element Plus UI组件
- ✅ 模块化API设计
- ✅ 响应式布局
- ✅ 路由守卫
- ✅ 请求拦截器
- ✅ 错误处理
- ✅ 环境配置

## 更新日志

### v1.0.0 (2024-01-XX)

- 🎉 项目迁移到Vue3+TypeScript+Pinia技术栈
- ✨ 重构API模块，提供类型安全的接口
- ✨ 使用Pinia替代Vuex进行状态管理
- ✨ 优化项目结构和配置
- 🐛 修复路由守卫和权限验证
- 📝 完善文档和类型定义
